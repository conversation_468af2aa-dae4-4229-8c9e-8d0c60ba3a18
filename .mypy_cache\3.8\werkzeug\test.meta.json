{"data_mtime": 1753889304, "dep_lines": [33, 43, 44, 14, 18, 21, 28, 39, 41, 45, 49, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 51, 286, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.multipart", "werkzeug.wrappers.request", "werkzeug.wrappers.response", "urllib.parse", "werkzeug._internal", "werkzeug.datastructures", "werkzeug.http", "werkzeug.urls", "werkzeug.utils", "werkzeug.wsgi", "_typeshed.wsgi", "__future__", "dataclasses", "mimetypes", "sys", "typing", "collections", "datetime", "io", "itertools", "random", "tempfile", "time", "typing_extensions", "json", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "http", "json.encoder", "urllib", "werkzeug.datastructures.auth", "werkzeug.datastructures.file_storage", "werkzeug.datastructures.headers", "werkzeug.datastructures.structures", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers"], "hash": "778019b618b8466214bf31077417d7e64bbd015a", "id": "werkzeug.test", "ignore_all": true, "interface_hash": "7523966c001792dd502f1154f1ac202529a34fcb", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\test.py", "plugin_data": null, "size": 52633, "suppressed": [], "version_id": "1.15.0"}