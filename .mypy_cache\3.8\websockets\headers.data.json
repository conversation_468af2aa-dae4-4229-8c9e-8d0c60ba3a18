{".class": "MypyFile", "_fullname": "websockets.headers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ConnectionOption": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ConnectionOption", "kind": "Gdef", "module_public": false}, "ExtensionHeader": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionHeader", "kind": "Gdef", "module_public": false}, "ExtensionName": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionName", "kind": "Gdef", "module_public": false}, "ExtensionParameter": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.ExtensionParameter", "kind": "Gdef", "module_public": false}, "InvalidHeaderFormat": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderFormat", "kind": "Gdef", "module_public": false}, "InvalidHeaderValue": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidHeaderValue", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Subprotocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Subprotocol", "kind": "Gdef", "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.headers.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "UpgradeProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.UpgradeProtocol", "kind": "Gdef", "module_public": false}, "_OWS_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._OWS_re", "name": "_OWS_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.headers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.headers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.headers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.headers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.headers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.headers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_protocol_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._protocol_re", "name": "_protocol_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_quotable_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._quotable_re", "name": "_quotable_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_quote_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._quote_re", "name": "_quote_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_quoted_string_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._quoted_string_re", "name": "_quoted_string_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_token68_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._token68_re", "name": "_token68_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_token_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._token_re", "name": "_token_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_unquote_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers._unquote_re", "name": "_unquote_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef", "module_public": false}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "build_authorization_basic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["username", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_authorization_basic", "name": "build_authorization_basic", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["username", "password"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_authorization_basic", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_extension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_extension", "name": "build_extension", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["extensions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionHeader"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_extension", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_extension_item": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_extension_item", "name": "build_extension_item", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "parameters"], "arg_types": ["websockets.typing.ExtensionName", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_extension_item", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_extension_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers.build_extension_list", "name": "build_extension_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["extensions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionHeader"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_host": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["host", "port", "secure", "always_include_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_host", "name": "build_host", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["host", "port", "secure", "always_include_port"], "arg_types": ["builtins.str", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_host", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_quoted_string": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_quoted_string", "name": "build_quoted_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_quoted_string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_subprotocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["subprotocols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_subprotocol", "name": "build_subprotocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["subprotocols"], "arg_types": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_subprotocol", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_subprotocol_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers.build_subprotocol_list", "name": "build_subprotocol_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["subprotocols"], "arg_types": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_www_authenticate_basic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["realm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.build_www_authenticate_basic", "name": "build_www_authenticate_basic", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["realm"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_www_authenticate_basic", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "ipaddress": {".class": "SymbolTableNode", "cross_ref": "ipaddress", "kind": "Gdef", "module_public": false}, "parse_OWS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["header", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_OWS", "name": "parse_OWS", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["header", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_OWS", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_authorization_basic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_authorization_basic", "name": "parse_authorization_basic", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_authorization_basic", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_connection", "name": "parse_connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_connection", "ret_type": {".class": "Instance", "args": ["websockets.typing.ConnectionOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_connection_option": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_connection_option", "name": "parse_connection_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_connection_option", "ret_type": {".class": "TupleType", "implicit": false, "items": ["websockets.typing.ConnectionOption", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_end": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_end", "name": "parse_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_end", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_extension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_extension", "name": "parse_extension", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_extension", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionHeader"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_extension_item": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_extension_item", "name": "parse_extension_item", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_extension_item", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionHeader"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_extension_item_param": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_extension_item_param", "name": "parse_extension_item_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_extension_item_param", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionParameter"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_extension_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers.parse_extension_list", "name": "parse_extension_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.ExtensionHeader"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["parse_item", "header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_list", "name": "parse_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["parse_item", "header", "pos", "header_name"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.headers.T", "id": -1, "name": "T", "namespace": "websockets.headers.parse_list", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_list", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.headers.T", "id": -1, "name": "T", "namespace": "websockets.headers.parse_list", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.headers.T", "id": -1, "name": "T", "namespace": "websockets.headers.parse_list", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "parse_quoted_string": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_quoted_string", "name": "parse_quoted_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_quoted_string", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_subprotocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_subprotocol", "name": "parse_subprotocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_subprotocol", "ret_type": {".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_subprotocol_item": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_subprotocol_item", "name": "parse_subprotocol_item", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_subprotocol_item", "ret_type": {".class": "TupleType", "implicit": false, "items": ["websockets.typing.Subprotocol", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_subprotocol_list": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.headers.parse_subprotocol_list", "name": "parse_subprotocol_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_token": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_token", "name": "parse_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_token", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_token68": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_token68", "name": "parse_token68", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_token68", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_upgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_upgrade", "name": "parse_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_upgrade", "ret_type": {".class": "Instance", "args": ["websockets.typing.UpgradeProtocol"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_upgrade_protocol": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.parse_upgrade_protocol", "name": "parse_upgrade_protocol", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["header", "pos", "header_name"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_upgrade_protocol", "ret_type": {".class": "TupleType", "implicit": false, "items": ["websockets.typing.UpgradeProtocol", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "peek_ahead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["header", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.peek_ahead", "name": "peek_ahead", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["header", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "peek_ahead", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "validate_subprotocols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["subprotocols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.headers.validate_subprotocols", "name": "validate_subprotocols", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["subprotocols"], "arg_types": [{".class": "Instance", "args": ["websockets.typing.Subprotocol"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_subprotocols", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\websockets\\headers.py"}