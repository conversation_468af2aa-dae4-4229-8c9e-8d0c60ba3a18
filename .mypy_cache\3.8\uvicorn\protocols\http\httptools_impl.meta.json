{"data_mtime": 1753889309, "dep_lines": [24, 25, 8, 14, 22, 23, 26, 1, 3, 4, 5, 6, 7, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["uvicorn.protocols.http.flow_control", "uvicorn.protocols.utils", "asyncio.events", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "http", "logging", "re", "urllib", "collections", "typing", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.locks", "asyncio.protocols", "asyncio.transports", "enum", "types", "typing_extensions", "urllib.parse", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.websockets", "uvicorn.protocols.websockets.websockets_impl", "uvicorn.protocols.websockets.wsproto_impl"], "hash": "592ea7044493c20c687442e1b9e78fd374913a75", "id": "uvicorn.protocols.http.httptools_impl", "ignore_all": true, "interface_hash": "feb3de635825aca30894223f61fd967444004418", "mtime": 1750470764, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py", "plugin_data": null, "size": 21805, "suppressed": ["httptools"], "version_id": "1.15.0"}