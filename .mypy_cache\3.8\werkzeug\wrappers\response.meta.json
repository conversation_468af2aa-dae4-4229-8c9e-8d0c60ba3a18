{"data_mtime": 1753889304, "dep_lines": [10, 28, 6, 8, 9, 11, 13, 14, 16, 25, 235, 677, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.response", "werkzeug.wrappers.request", "urllib.parse", "werkzeug.datastructures", "werkzeug.http", "werkzeug.urls", "werkzeug.utils", "werkzeug.wsgi", "werkzeug._internal", "_typeshed.wsgi", "werkzeug.test", "werkzeug.exceptions", "__future__", "json", "typing", "http", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "enum", "time", "types", "typing_extensions", "werkzeug.datastructures.etag", "werkzeug.datastructures.headers", "werkzeug.datastructures.range", "werkzeug.sansio", "werkzeug.sansio.request"], "hash": "1ac6f51ca3390f91e01471e0c51684b04a6db192", "id": "werkzeug.wrappers.response", "ignore_all": true, "interface_hash": "c277881b1d9e2347cf5f7df9e8c73304a4ca377b", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\wrappers\\response.py", "plugin_data": null, "size": 32587, "suppressed": [], "version_id": "1.15.0"}