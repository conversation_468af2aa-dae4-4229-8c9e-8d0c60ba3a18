Add reasoning model support and enhanced mathematical formatting

Major Features:
- Native reasoning model support (DeepSeek-R1, Qwen3, O1) with thinking process display
- Comprehensive LaTeX mathematical formatting with superscripts, fractions, and symbols
- Real-time thinking content streaming with collapsible interface
- Added quadratic equation test prompt to Mathematics section

Technical Improvements:
- Fixed Pydantic serialization bug (dict() → model_dump()) for thinking field preservation
- Enhanced Ollama integration with GenerateRequest import and native thinking mode
- Improved type safety with null filtering in model discovery
- Added thinking, reasoning_tokens, reasoning_duration fields to response models

Frontend Enhancements:
- Advanced mathematical notation rendering (LaTeX inline/display math, boxed answers)
- Automatic superscript conversion (x2 → x², (5)2 → (5)²)
- Professional fraction display with horizontal bars
- Square root styling and mathematical symbol conversion

Bug Fixes:
- Resolved type error in proxy_manager.py line 105 (Optional[str] index issue)
- Fixed thinking content accumulation in streaming responses
- Enhanced mathematical formatting pattern matching and processing order
