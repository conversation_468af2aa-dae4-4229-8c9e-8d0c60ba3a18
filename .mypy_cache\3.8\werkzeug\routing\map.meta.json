{"data_mtime": **********, "dep_lines": [21, 22, 28, 29, 37, 7, 11, 13, 15, 19, 20, 33, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.routing.converters", "werkzeug.routing.exceptions", "werkzeug.routing.matcher", "werkzeug.routing.rules", "werkzeug.wrappers.request", "urllib.parse", "werkzeug._internal", "werkzeug.datastructures", "werkzeug.exceptions", "werkzeug.urls", "werkzeug.wsgi", "_typeshed.wsgi", "__future__", "typing", "warnings", "pprint", "threading", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "abc", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.wrappers"], "hash": "96761e7aba104855eca749da4e44e2acd5ab13b7", "id": "werkzeug.routing.map", "ignore_all": true, "interface_hash": "076a03b03729722377e341b6fd6f9dde273f81aa", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\routing\\map.py", "plugin_data": null, "size": 36350, "suppressed": [], "version_id": "1.15.0"}