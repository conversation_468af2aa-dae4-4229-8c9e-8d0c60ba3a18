{"data_mtime": 1753889304, "dep_lines": [1, 3, 4, 5, 6, 7, 9, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["__future__", "http", "logging", "sys", "copy", "typing", "click", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "5d1117bea5f4e1218cbfa1f014a5f58d6ba67603", "id": "uvicorn.logging", "ignore_all": true, "interface_hash": "62ca1b2334c7684433f31bf3e7808aa1eb624f23", "mtime": 1750470764, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\logging.py", "plugin_data": null, "size": 4235, "suppressed": [], "version_id": "1.15.0"}