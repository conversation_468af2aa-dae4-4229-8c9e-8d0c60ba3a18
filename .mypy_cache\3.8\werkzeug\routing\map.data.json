{".class": "MypyFile", "_fullname": "werkzeug.routing.map", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadHost": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.BadHost", "kind": "Gdef"}, "BaseConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.BaseConverter", "kind": "Gdef"}, "BuildError": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.BuildError", "kind": "Gdef"}, "DEFAULT_CONVERTERS": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.DEFAULT_CONVERTERS", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.HTTPException", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.ImmutableDict", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "threading.Lock", "kind": "Gdef"}, "Map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.map.Map", "name": "Map", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.map", "mro": ["werkzeug.routing.map.Map", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "rules", "default_subdomain", "strict_slashes", "merge_slashes", "redirect_defaults", "converters", "sort_parameters", "sort_key", "host_matching"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "rules", "default_subdomain", "strict_slashes", "merge_slashes", "redirect_defaults", "converters", "sort_parameters", "sort_key", "host_matching"], "arg_types": ["werkzeug.routing.map.Map", {".class": "UnionType", "items": [{".class": "Instance", "args": ["werkzeug.routing.rules.RuleFactory"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "werkzeug.routing.converters.BaseConverter"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Map", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["werkzeug.routing.map.Map"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Map", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_matcher": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map._matcher", "name": "_matcher", "type": "werkzeug.routing.matcher.StateMachineMatcher"}}, "_remap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map._remap", "name": "_remap", "type": "builtins.bool"}}, "_remap_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map._remap_lock", "name": "_remap_lock", "type": "_thread.LockType"}}, "_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "werkzeug.routing.map.Map._rules", "name": "_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.routing.map.Map"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rules of Map", "ret_type": {".class": "Instance", "args": ["werkzeug.routing.rules.Rule"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "werkzeug.routing.map.Map._rules", "name": "_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.routing.map.Map"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rules of Map", "ret_type": {".class": "Instance", "args": ["werkzeug.routing.rules.Rule"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_rules_by_endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "werkzeug.routing.map.Map._rules_by_endpoint", "name": "_rules_by_endpoint", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["werkzeug.routing.rules.Rule"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rulefactory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rulefactory"], "arg_types": ["werkzeug.routing.map.Map", "werkzeug.routing.rules.RuleFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of Map", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "server_name", "script_name", "subdomain", "url_scheme", "default_method", "path_info", "query_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "server_name", "script_name", "subdomain", "url_scheme", "default_method", "path_info", "query_args"], "arg_types": ["werkzeug.routing.map.Map", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of Map", "ret_type": "werkzeug.routing.map.MapAdapter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind_to_environ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "environ", "server_name", "subdomain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.bind_to_environ", "name": "bind_to_environ", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "environ", "server_name", "subdomain"], "arg_types": ["werkzeug.routing.map.Map", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "werkzeug.wrappers.request.Request"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_to_environ of Map", "ret_type": "werkzeug.routing.map.MapAdapter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "converters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.converters", "name": "converters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "werkzeug.routing.converters.BaseConverter"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "default_converters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.map.Map.default_converters", "name": "default_converters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "werkzeug.routing.converters.BaseConverter"}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.ImmutableDict"}}}, "default_subdomain": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.default_subdomain", "name": "default_subdomain", "type": "builtins.str"}}, "host_matching": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.host_matching", "name": "host_matching", "type": "builtins.bool"}}, "is_endpoint_expecting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "endpoint", "arguments"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.is_endpoint_expecting", "name": "is_endpoint_expecting", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "endpoint", "arguments"], "arg_types": ["werkzeug.routing.map.Map", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_endpoint_expecting of Map", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "endpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.iter_rules", "name": "iter_rules", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "endpoint"], "arg_types": ["werkzeug.routing.map.Map", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_rules of Map", "ret_type": {".class": "Instance", "args": ["werkzeug.routing.rules.Rule"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lock_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.map.Map.lock_class", "name": "lock_class", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["_thread.LockType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "_thread.LockType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_slashes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.merge_slashes", "name": "merge_slashes", "type": "builtins.bool"}}, "redirect_defaults": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.redirect_defaults", "name": "redirect_defaults", "type": "builtins.bool"}}, "sort_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.sort_key", "name": "sort_key", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "sort_parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.sort_parameters", "name": "sort_parameters", "type": "builtins.bool"}}, "strict_slashes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.Map.strict_slashes", "name": "strict_slashes", "type": "builtins.bool"}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.Map.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.routing.map.Map"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of Map", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.map.Map.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.map.Map", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MapAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.map.MapAdapter", "name": "MapAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.map", "mro": ["werkzeug.routing.map.MapAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "map", "server_name", "script_name", "subdomain", "url_scheme", "path_info", "default_method", "query_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "map", "server_name", "script_name", "subdomain", "url_scheme", "path_info", "default_method", "query_args"], "arg_types": ["werkzeug.routing.map.MapAdapter", "werkzeug.routing.map.Map", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MapAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_partial_build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "endpoint", "values", "method", "append_unknown"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter._partial_build", "name": "_partial_build", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "endpoint", "values", "method", "append_unknown"], "arg_types": ["werkzeug.routing.map.MapAdapter", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_partial_build of MapAdapter", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "path_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.allowed_methods", "name": "allowed_methods", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "path_info"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allowed_methods of MapAdapter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint", "values", "method", "force_external", "append_unknown", "url_scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "endpoint", "values", "method", "force_external", "append_unknown", "url_scheme"], "arg_types": ["werkzeug.routing.map.MapAdapter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of MapAdapter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.default_method", "name": "default_method", "type": "builtins.str"}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "view_func", "path_info", "method", "catch_http_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.dispatch", "name": "dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "view_func", "path_info", "method", "catch_http_exceptions"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dispatch of MapAdapter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_query_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.encode_query_args", "name": "encode_query_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_args"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_query_args of MapAdapter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "rule", "method", "values", "query_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.get_default_redirect", "name": "get_default_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "rule", "method", "values", "query_args"], "arg_types": ["werkzeug.routing.map.MapAdapter", "werkzeug.routing.rules.Rule", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_redirect of MapAdapter", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "domain_part"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.get_host", "name": "get_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain_part"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_host of MapAdapter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_alias_redirect_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "path", "endpoint", "values", "method", "query_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.make_alias_redirect_url", "name": "make_alias_redirect_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "path", "endpoint", "values", "method", "query_args"], "arg_types": ["werkzeug.routing.map.MapAdapter", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_alias_redirect_url of MapAdapter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_redirect_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path_info", "query_args", "domain_part"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.make_redirect_url", "name": "make_redirect_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path_info", "query_args", "domain_part"], "arg_types": ["werkzeug.routing.map.MapAdapter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_redirect_url of MapAdapter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.map", "name": "map", "type": "werkzeug.routing.map.Map"}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.match", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "werkzeug.routing.map.MapAdapter.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", "werkzeug.routing.rules.Rule"], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.routing.map.MapAdapter.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.routing.map.MapAdapter.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["werkzeug.routing.rules.Rule", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["werkzeug.routing.rules.Rule", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "path_info", "method", "return_rule", "query_args", "websocket"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of MapAdapter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["werkzeug.routing.rules.Rule", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "path_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.path_info", "name": "path_info", "type": "builtins.str"}}, "query_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.query_args", "name": "query_args", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "script_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.script_name", "name": "script_name", "type": "builtins.str"}}, "server_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.server_name", "name": "server_name", "type": "builtins.str"}}, "subdomain": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.subdomain", "name": "subdomain", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "path_info", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.map.MapAdapter.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "path_info", "method"], "arg_types": ["werkzeug.routing.map.MapAdapter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of MapAdapter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url_scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.url_scheme", "name": "url_scheme", "type": "builtins.str"}}, "websocket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.map.MapAdapter.websocket", "name": "websocket", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.map.MapAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.map.MapAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MethodNotAllowed": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.MethodNotAllowed", "kind": "Gdef"}, "MultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.MultiDict", "kind": "Gdef"}, "NoMatch": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.NoMatch", "kind": "Gdef"}, "NotFound": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.NotFound", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "werkzeug.wrappers.request.Request", "kind": "Gdef"}, "RequestAliasRedirect": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestAliasRedirect", "kind": "Gdef"}, "RequestPath": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestPath", "kind": "Gdef"}, "RequestRedirect": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestRedirect", "kind": "Gdef"}, "Rule": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.Rule", "kind": "Gdef"}, "RuleFactory": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.RuleFactory", "kind": "Gdef"}, "StateMachineMatcher": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.matcher.StateMachineMatcher", "kind": "Gdef"}, "WSGIApplication": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIApplication", "kind": "Gdef"}, "WSGIEnvironment": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIEnvironment", "kind": "Gdef"}, "WebsocketMismatch": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.WebsocketMismatch", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.map.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.map.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.map.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.map.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.map.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.map.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_environ": {".class": "SymbolTableNode", "cross_ref": "werkzeug._internal._get_environ", "kind": "Gdef"}, "_simple_rule_re": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules._simple_rule_re", "kind": "Gdef"}, "_urlencode": {".class": "SymbolTableNode", "cross_ref": "werkzeug.urls._urlencode", "kind": "Gdef"}, "_wsgi_decoding_dance": {".class": "SymbolTableNode", "cross_ref": "werkzeug._internal._wsgi_decoding_dance", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "get_host": {".class": "SymbolTableNode", "cross_ref": "werkzeug.wsgi.get_host", "kind": "Gdef"}, "pformat": {".class": "SymbolTableNode", "cross_ref": "pprint.pformat", "kind": "Gdef"}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "urlunsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunsplit", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\routing\\map.py"}