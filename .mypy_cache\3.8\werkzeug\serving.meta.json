{"data_mtime": 1753889304, "dep_lines": [82, 382, 27, 29, 32, 34, 35, 80, 85, 797, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 38, 797, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 20, 5, 5, 5, 5, 5, 25, 25, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.rsa", "werkzeug.debug.tbtools", "http.server", "urllib.parse", "werkzeug._internal", "werkzeug.exceptions", "werkzeug.urls", "_typeshed.wsgi", "cryptography.x509", "importlib.metadata", "__future__", "errno", "io", "os", "selectors", "socket", "socketserver", "sys", "typing", "datetime", "ssl", "importlib", "builtins", "_frozen_importlib", "_io", "_socket", "_ssl", "_typeshed", "abc", "cryptography", "cryptography.hazmat", "cryptography.hazmat.primitives", "cryptography.hazmat.primitives.asymmetric", "cryptography.x509.base", "email", "email.message", "enum", "http", "traceback", "types", "typing_extensions", "urllib", "werkzeug.debug", "werkzeug.sansio", "werkzeug.sansio.response"], "hash": "71a0703628f08e80e4d6e1d1d327728b8a065483", "id": "werkzeug.serving", "ignore_all": true, "interface_hash": "d89b14f6af7091592d55d855f813fd6e747d4f67", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\serving.py", "plugin_data": null, "size": 39332, "suppressed": [], "version_id": "1.15.0"}