# LLM Proxy Server

A high-performance, load-balancing proxy server for Ollama instances with authentication, monitoring, and streaming support.

> **📚 Complete Documentation**: For comprehensive documentation, installation guides, API reference, and more, see **[docs/](docs/)** or start with the **[Documentation Index](docs/index.md)**

## ✨ Key Features

- **🔄 Load Balancing**: Multiple strategies (adaptive, fastest, least connections, round-robin)
- **🔐 Authentication**: User-based access control with admin privileges
- **📡 Streaming Support**: Full streaming support for chat and generate endpoints
- **🧠 Reasoning Models**: Native support for DeepSeek-R1, Qwen3, O1 with thinking process display
- **🔢 Mathematical Formatting**: LaTeX rendering, superscripts, fractions, and mathematical notation
- **📊 Monitoring**: Built-in metrics, health checks, and observability
- **🌐 Web Interface**: Modern web-based configuration and management
- **🔌 Ollama Compatibility**: Drop-in replacement for Ollama API
- **🐳 Docker Support**: Easy deployment with Docker Compose
- **⚡ High Performance**: Async/await architecture for maximum throughput

## 🚀 Quick Start

Get up and running in 5 minutes:

### Option 1: Docker Compose (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd llm-proxy-server

# Configure your Ollama servers
cp config/hosts.json.example config/hosts.json
# Edit hosts.json with your server details

# Start the stack
cd llm-proxy-server
docker-compose -f docker-compose.local.yml up -d

# Verify it's working
curl http://localhost:11440/proxy/health

# Access web configuration (if auth enabled, login first)
open http://localhost:11440/admin
```

### Option 2: Quick Test Setup

```bash
# Start with local testing configuration
docker-compose -f docker-compose.local.yml up -d

# Access Open WebUI
open http://localhost:3000

# Test the API
curl -X POST http://localhost:11440/api/chat \
  -H "Content-Type: application/json" \
  -d '{"model":"llama3.2","messages":[{"role":"user","content":"Hello!"}]}'
```

## 📖 Documentation

| Document | Description |
|----------|-------------|
| **[📋 Documentation Index](docs/index.md)** | Complete documentation navigation |
| **[🎯 Quick Start Guide](docs/quick-start.md)** | Get running in 5 minutes |
| **[📚 User Guide](docs/user-guide.md)** | How to use the proxy server |
| **[🔧 API Reference](docs/api-reference.md)** | Complete API documentation |
| **[⚙️ Configuration Guide](docs/configuration.md)** | All configuration options |
| **[🌐 Web Configuration](docs/web-configuration.md)** | Web-based admin interface |
| **[🚀 Deployment Guide](docs/deployment.md)** | Production deployment |
| **[🔍 Troubleshooting](docs/troubleshooting.md)** | Common issues and solutions |

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   Open WebUI    │    │   API Clients   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    LLM Proxy Server       │
                    │  ┌─────────────────────┐  │
                    │  │   Load Balancer     │  │
                    │  │   Authentication    │  │
                    │  │   Monitoring        │  │
                    │  └─────────────────────┘  │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────▼─────┐           ┌─────▼─────┐           ┌─────▼─────┐
    │  Ollama   │           │  Ollama   │           │  Ollama   │
    │Instance 1 │           │Instance 2 │           │Instance 3 │
    └───────────┘           └───────────┘           └───────────┘
```

## 🎯 Use Cases

- **🖥️ Multi-GPU Setups**: Distribute workloads across GPU-equipped machines
- **🔄 High Availability**: Provide redundancy and failover for critical applications
- **👥 Team Sharing**: Share LLM resources across development teams
- **📈 Production Scaling**: Scale inference capacity horizontally
- **💰 Cost Optimization**: Efficiently utilize existing hardware resources

## 🛠️ Development

```bash
# Set up development environment
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
pip install -e .

# Run tests
python -m pytest

# Start development server
python -m llm_proxy_server.main
```

See the **[Development Guide](docs/development.md)** for complete setup instructions.

## 📊 Monitoring

Built-in monitoring endpoints:

- **Health**: `GET /proxy/health` - Basic health status
- **Status**: `GET /proxy/status` - Detailed system status
- **Metrics**: `GET /proxy/metrics` - Comprehensive metrics

See the **[Monitoring Guide](docs/monitoring.md)** for dashboard setup and alerting.

## 🔐 Security

- File-based authentication with role-based access control
- Admin and regular user roles with different permissions
- HTTPS support with reverse proxy integration
- Request validation and error handling

See the **[Authentication Guide](docs/authentication.md)** for security setup.

## 🤝 Contributing

We welcome contributions! Please see:

- **[Development Guide](docs/development.md)** - Development environment setup
- **[Testing Guide](docs/testing.md)** - Testing strategies and tools
- **[Architecture Guide](docs/architecture.md)** - System design and components
- **[Future Enhancements](docs/future-enhancements.md)** - Roadmap and planned features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

**USE AT YOUR OWN RISK**: This software is provided for educational and experimental purposes. The authors disclaim all responsibility for data loss, service interruptions, security issues, or any other problems that may arise from using this software. Users are solely responsible for:

- Testing thoroughly before production use
- Implementing appropriate security measures
- Monitoring and maintaining their deployments
- Ensuring compliance with applicable laws and regulations
- Evaluating AI model outputs for accuracy and appropriateness

See the LICENSE file for complete disclaimer terms.

## 🆘 Support

- **📖 Documentation**: Check the [complete documentation](docs/)
- **🐛 Issues**: Report bugs on GitHub Issues
- **💬 Discussions**: Join community discussions
- **🔧 Troubleshooting**: See the [troubleshooting guide](docs/troubleshooting.md)

---

**🚀 Ready to get started?** Follow the **[Quick Start Guide](docs/quick-start.md)** or explore the **[Documentation Index](docs/index.md)** for comprehensive information!