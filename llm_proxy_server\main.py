"""Main FastAPI application for LLM Proxy Server"""

import asyncio
import json
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Optional

import structlog
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles

from . import __version__
from .auth import AuthManager
from .config import get_settings
from .config_api import config_router
from .load_balancer import LoadBalancer
from .models import (
    ChatRequest,
    GenerateRequest,
    EmbedRequest,
    ShowRequest,
    ModelsResponse,
    PullModelRequest,
    PullModelResponse,
    DeleteModelRequest,
    DeleteModelResponse,
    ModelManagementStatus,
    ProxyStatus,
    HealthResponse
)
from .monitoring import MetricsManager
from .proxy_manager import ProxyManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Global instances
settings = get_settings()
auth_manager = AuthManager(settings.auth_config_path)
load_balancer = LoadBalancer(settings.load_balancer_strategy)
metrics_manager = MetricsManager()
proxy_manager = ProxyManager(settings, load_balancer, metrics_manager)

# Set global auth manager for config API
from . import auth
auth.auth_manager = auth_manager

# Set global model registry refresh callback for config API
from . import config_api
config_api.set_model_registry_refresh_callback(proxy_manager.refresh_model_registry)

# Security
security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Dict:
    """Get current authenticated user"""
    if not settings.auth_enabled:
        logger.debug("Auth disabled, granting admin access", auth_enabled=settings.auth_enabled)
        return {"username": "anonymous", "is_admin": True}  # Grant admin access when auth is disabled
    
    if not credentials:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    user = await auth_manager.authenticate(credentials.credentials)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid authentication")
    
    # Log the request
    logger.info(
        "Request authenticated",
        user=user["username"],
        endpoint=request.url.path,
        method=request.method,
        client_ip=request.client.host if request.client else "unknown"
    )
    
    return user


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting LLM Proxy Server", version=__version__)

    # Initialize proxy manager
    await proxy_manager.initialize()

    # Start background tasks
    logger.info("Starting health check loop with interval", interval=settings.health_check_interval)
    health_check_task = asyncio.create_task(health_check_loop())
    
    yield
    
    # Cleanup
    health_check_task.cancel()
    await proxy_manager.shutdown()
    logger.info("LLM Proxy Server stopped")


async def health_check_loop():
    """Background health check loop"""
    logger.info("Health check loop started")
    model_registry_refresh_counter = 0
    model_registry_refresh_interval = 5  # Refresh model registry every 5 health checks

    while True:
        try:
            await asyncio.sleep(settings.health_check_interval)
            # Perform health checks on all configured hosts
            await proxy_manager.perform_health_checks()

            # Periodically refresh model registry to catch new/removed models
            model_registry_refresh_counter += 1
            if model_registry_refresh_counter >= model_registry_refresh_interval:
                logger.info("Refreshing model registry as part of health check")
                await proxy_manager.refresh_model_registry()
                model_registry_refresh_counter = 0

        except asyncio.CancelledError:
            logger.info("Health check loop cancelled")
            break
        except Exception as e:
            logger.error("Health check failed", error=str(e))


# Create FastAPI app
app = FastAPI(
    title="LLM Proxy Server",
    description="A load-balancing proxy server for Ollama instances",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include configuration API router
app.include_router(config_router)

# Mount static files for web interface
from pathlib import Path
static_dir = Path(__file__).parent.parent / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Add route to serve login page
@app.get("/login")
async def login_page():
    """Serve the login page"""
    from fastapi.responses import FileResponse
    login_file = static_dir / "login.html"
    if login_file.exists():
        return FileResponse(str(login_file))
    else:
        raise HTTPException(status_code=404, detail="Login page not found")

# Add route to serve configuration interface
@app.get("/admin")
async def admin_interface():
    """Serve the configuration interface"""
    from fastapi.responses import FileResponse
    config_file = static_dir / "config.html"
    if config_file.exists():
        return FileResponse(str(config_file))
    else:
        raise HTTPException(status_code=404, detail="Configuration interface not found")

# Add route to serve configuration interface (alias for /admin)
@app.get("/config")
async def config_interface():
    """Serve the configuration interface (alias for /admin)"""
    from fastapi.responses import FileResponse
    config_file = static_dir / "config.html"
    if config_file.exists():
        return FileResponse(str(config_file))
    else:
        raise HTTPException(status_code=404, detail="Configuration interface not found")


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Log all requests"""
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    
    logger.info(
        "Request processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
        client_ip=request.client.host if request.client else "unknown"
    )
    
    return response


# Ollama API endpoints
@app.post("/api/chat")
async def chat(
    request: ChatRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle chat requests with streaming support"""
    start_time = time.time()
    
    try:
        
        if request.stream:
            async def stream_chat():
                async for chunk in proxy_manager.handle_chat_stream(request, user):
                    yield f"{json.dumps(chunk.model_dump())}\n"
            
            return StreamingResponse(
                stream_chat(),
                media_type="application/x-ndjson",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            response = await proxy_manager.handle_chat(request, user)
            return response
            
    except Exception as e:
        metrics_manager.record_request("/api/chat", "POST", 500, time.time() - start_time, model=request.model, error=type(e).__name__)
        logger.error("Chat request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/generate")
async def generate(
    request: GenerateRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle generate requests with streaming support"""
    start_time = time.time()



    try:
        
        if request.stream:
            async def stream_generate():
                async for chunk in proxy_manager.handle_generate_stream(request, user):

                    yield f"{json.dumps(chunk.model_dump())}\n"
            
            return StreamingResponse(
                stream_generate(),
                media_type="application/x-ndjson",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            response = await proxy_manager.handle_generate(request, user)
            return response
            
    except Exception as e:
        metrics_manager.record_request("/api/generate", "POST", 500, time.time() - start_time, model=request.model, error=type(e).__name__)
        logger.error("Generate request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/embed")
async def embed(
    request: EmbedRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle embed requests"""
    try:
        response = await proxy_manager.handle_embed(request, user)
        return response
    except Exception as e:
        logger.error("Embed request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/show")
async def show(
    request: ShowRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle show requests"""
    start_time = time.time()

    try:
        response = await proxy_manager.handle_show(request, user)
        # Note: ShowResponse doesn't have proxy_server field, so we can't track host for this endpoint
        metrics_manager.record_request("/api/show", "POST", 200, time.time() - start_time, model=request.model)
        return response
    except Exception as e:
        metrics_manager.record_request("/api/show", "POST", 500, time.time() - start_time, model=request.model, error=type(e).__name__)
        logger.error("Show request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/tags")
async def list_models(
    refresh: bool = False,
    user: Dict = Depends(get_current_user)
) -> ModelsResponse:
    """List all available models, optionally refreshing the model registry first"""
    start_time = time.time()

    try:
        # If refresh is requested, refresh the model registry first
        if refresh:
            await proxy_manager.refresh_model_registry()

        models = await proxy_manager.list_models()
        metrics_manager.record_request("/api/tags", "GET", 200, time.time() - start_time)
        return ModelsResponse(models=models)
    except Exception as e:
        metrics_manager.record_request("/api/tags", "GET", 500, time.time() - start_time, error=type(e).__name__)
        logger.error("List models failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/version")
async def version():
    """Get API version"""
    start_time = time.time()

    try:
        response = {"version": "0.5.4"}  # Ollama compatibility
        metrics_manager.record_request("/api/version", "GET", 200, time.time() - start_time)
        return response
    except Exception as e:
        metrics_manager.record_request("/api/version", "GET", 500, time.time() - start_time, error=type(e).__name__)
        raise


# Model management endpoints
@app.post("/api/pull")
async def pull_model(
    request: PullModelRequest,
    user: Dict = Depends(get_current_user)
) -> PullModelResponse:
    """Pull/install a model on specified servers"""
    start_time = time.time()

    try:
        response = await proxy_manager.pull_model(request, user)
        metrics_manager.record_request("/api/pull", "POST", 200, time.time() - start_time, model=request.name)
        return response
    except Exception as e:
        metrics_manager.record_request("/api/pull", "POST", 500, time.time() - start_time, model=request.name, error=type(e).__name__)
        logger.error("Pull model request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/delete")
async def delete_model(
    request: DeleteModelRequest,
    user: Dict = Depends(get_current_user)
) -> DeleteModelResponse:
    """Delete a model from specified servers"""
    start_time = time.time()

    try:
        response = await proxy_manager.delete_model(request, user)
        metrics_manager.record_request("/api/delete", "POST", 200, time.time() - start_time, model=request.name)
        return response
    except Exception as e:
        metrics_manager.record_request("/api/delete", "POST", 500, time.time() - start_time, model=request.name, error=type(e).__name__)
        logger.error("Delete model request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/model/{model_name}/status")
async def get_model_status(
    model_name: str,
    user: Dict = Depends(get_current_user)
) -> ModelManagementStatus:
    """Get the status of a model across all servers"""
    start_time = time.time()

    try:
        response = await proxy_manager.get_model_status(model_name)
        metrics_manager.record_request("/api/model/status", "GET", 200, time.time() - start_time, model=model_name)
        return response
    except Exception as e:
        metrics_manager.record_request("/api/model/status", "GET", 500, time.time() - start_time, model=model_name, error=type(e).__name__)
        logger.error("Get model status request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))



# Proxy management endpoints
@app.get("/proxy/status")
async def get_status(user: Dict = Depends(get_current_user)) -> ProxyStatus:
    """Get proxy server status"""
    start_time = time.time()

    if not user.get("is_admin", False):
        metrics_manager.record_request("/proxy/status", "GET", 403, time.time() - start_time, error="Forbidden")
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        response = await proxy_manager.get_status()
        metrics_manager.record_request("/proxy/status", "GET", 200, time.time() - start_time)
        return response
    except Exception as e:
        metrics_manager.record_request("/proxy/status", "GET", 500, time.time() - start_time, error=type(e).__name__)
        raise


@app.get("/proxy/metrics")
async def get_metrics(user: Dict = Depends(get_current_user)):
    """Get proxy metrics"""
    start_time = time.time()
    logger.debug("Metrics request", user=user, auth_enabled=settings.auth_enabled)
    if not user.get("is_admin", False):
        logger.warning("Admin access denied", user=user, auth_enabled=settings.auth_enabled)
        metrics_manager.record_request("/proxy/metrics", "GET", 403, time.time() - start_time, error="Forbidden")
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        # Get currently configured host URLs for status determination
        # Use ConfigManager directly to ensure we get the current configuration
        from .config_manager import ConfigManager
        config_manager = ConfigManager(
            hosts_config_path=settings.hosts_config_path,
            auth_config_path=settings.auth_config_path
        )
        hosts_config = config_manager.get_hosts()
        configured_host_urls = [host['host'] for host in hosts_config]

        logger.info("Configured hosts loaded for metrics", configured_hosts=configured_host_urls)

        response = metrics_manager.generate_metrics(configured_host_urls)
        metrics_manager.record_request("/proxy/metrics", "GET", 200, time.time() - start_time)
        return response
    except Exception as e:
        metrics_manager.record_request("/proxy/metrics", "GET", 500, time.time() - start_time, error=type(e).__name__)
        raise

@app.post("/proxy/metrics/reset")
async def reset_metrics(user: Dict = Depends(get_current_user)):
    """Reset all metrics data"""
    start_time = time.time()

    logger.debug("Metrics reset request", user=user, auth_enabled=settings.auth_enabled)
    if not user.get("is_admin", False):
        logger.warning("Admin access denied for metrics reset", user=user, auth_enabled=settings.auth_enabled)
        metrics_manager.record_request("/proxy/metrics/reset", "POST", 403, time.time() - start_time, error="Forbidden")
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        metrics_manager.reset_metrics()
        metrics_manager.record_request("/proxy/metrics/reset", "POST", 200, time.time() - start_time)
        logger.info("Metrics reset by admin", user=user.get("username"))
        return {"message": "Metrics reset successfully", "timestamp": datetime.now()}
    except Exception as e:
        metrics_manager.record_request("/proxy/metrics/reset", "POST", 500, time.time() - start_time, error=type(e).__name__)
        logger.error("Failed to reset metrics", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/debug/hosts")
async def debug_hosts():
    """Debug endpoint to check host configuration"""
    try:
        from .config_manager import ConfigManager
        config_manager = ConfigManager(
            hosts_config_path=settings.hosts_config_path,
            auth_config_path=settings.auth_config_path
        )
        hosts_config = config_manager.get_hosts()
        configured_host_urls = [host['host'] for host in hosts_config]

        # Get metrics hosts
        all_hosts_with_metrics = set(list(metrics_manager.host_request_count.keys()) + list(metrics_manager.health_checks.keys()))

        return {
            "configured_hosts": configured_host_urls,
            "hosts_with_metrics": list(all_hosts_with_metrics),
            "configured_count": len(configured_host_urls),
            "metrics_count": len(all_hosts_with_metrics)
        }
    except Exception as e:
        return {"error": str(e)}

@app.post("/proxy/metrics/cleanup")
async def cleanup_orphaned_hosts(user: Dict = Depends(get_current_user)):
    """Clean up metrics for hosts that are no longer configured"""
    start_time = time.time()

    if not user.get("is_admin", False):
        metrics_manager.record_request("/proxy/metrics/cleanup", "POST", 403, time.time() - start_time, error="Forbidden")
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        # Get currently configured host URLs
        from .config_manager import ConfigManager
        config_manager = ConfigManager(
            hosts_config_path=settings.hosts_config_path,
            auth_config_path=settings.auth_config_path
        )
        hosts_config = config_manager.get_hosts()
        configured_host_urls = [host['host'] for host in hosts_config]

        # Clean up orphaned hosts
        metrics_manager.cleanup_orphaned_hosts(configured_host_urls)

        metrics_manager.record_request("/proxy/metrics/cleanup", "POST", 200, time.time() - start_time)
        return {
            "status": "success",
            "message": "Orphaned host metrics cleaned up",
            "configured_hosts": configured_host_urls
        }
    except Exception as e:
        metrics_manager.record_request("/proxy/metrics/cleanup", "POST", 500, time.time() - start_time, error=type(e).__name__)
        logger.error("Metrics cleanup failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/proxy/metrics/save")
async def save_metrics(user: Dict = Depends(get_current_user)):
    """Manually save metrics to disk"""
    start_time = time.time()

    logger.debug("Metrics save request", user=user, auth_enabled=settings.auth_enabled)
    if not user.get("is_admin", False):
        logger.warning("Admin access denied for metrics save", user=user, auth_enabled=settings.auth_enabled)
        metrics_manager.record_request("/proxy/metrics/save", "POST", 403, time.time() - start_time, error="Forbidden")
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        metrics_manager.save_metrics_now()
        metrics_manager.record_request("/proxy/metrics/save", "POST", 200, time.time() - start_time)
        logger.info("Metrics manually saved by admin", user=user.get("username"))
        return {"message": "Metrics saved successfully", "timestamp": datetime.now()}
    except Exception as e:
        metrics_manager.record_request("/proxy/metrics/save", "POST", 500, time.time() - start_time, error=type(e).__name__)
        logger.error("Failed to save metrics", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/proxy/health")
async def health_check() -> HealthResponse:
    """Health check endpoint"""
    start_time = time.time()

    try:
        health_status = metrics_manager.get_health_status()

        response = HealthResponse(
            status=health_status["status"],
            timestamp=datetime.now(),
            version="1.0.0",
            uptime=health_status["uptime"]
        )

        metrics_manager.record_request("/proxy/health", "GET", 200, time.time() - start_time)
        return response
    except Exception as e:
        metrics_manager.record_request("/proxy/health", "GET", 500, time.time() - start_time, error=type(e).__name__)
        raise


@app.post("/debug/generate-request")
async def debug_generate_request(request: GenerateRequest):
    """Debug endpoint to check request parsing"""
    return {
        "model": request.model,
        "prompt": request.prompt,
        "host": getattr(request, 'host', 'NOT_SET'),
        "host_value": request.host,
        "all_fields": request.model_dump()
    }


@app.get("/")
async def root():
    """Root endpoint - Ollama compatibility"""
    start_time = time.time()

    try:
        response = {"message": "Ollama is running"}
        metrics_manager.record_request("/", "GET", 200, time.time() - start_time)
        return response
    except Exception as e:
        metrics_manager.record_request("/", "GET", 500, time.time() - start_time, error=type(e).__name__)
        raise


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "llm_proxy_server.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )