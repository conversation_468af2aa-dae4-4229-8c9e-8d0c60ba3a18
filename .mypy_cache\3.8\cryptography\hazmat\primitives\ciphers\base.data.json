{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives.ciphers.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AEADCipherContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["authenticate_additional_data", 1], ["finalize", 1], ["update", 1], ["update_into", 1]], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base.CipherContext"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "name": "AEADCipherContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "authenticate_additional_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext.authenticate_additional_data", "name": "authenticate_additional_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticate_additional_data of AEADCipherContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext.authenticate_additional_data", "name": "authenticate_additional_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticate_additional_data of AEADCipherContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AEADDecryptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["authenticate_additional_data", 1], ["finalize", 1], ["finalize_with_tag", 1], ["update", 1], ["update_into", 1]], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base.AEADCipherContext"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "name": "AEADDecryptionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "finalize_with_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext.finalize_with_tag", "name": "finalize_with_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_with_tag of AEADDecryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext.finalize_with_tag", "name": "finalize_with_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_with_tag of AEADDecryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AEADEncryptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["authenticate_additional_data", 1], ["finalize", 1], ["tag", 1], ["update", 1], ["update_into", 1]], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base.AEADCipherContext"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "name": "AEADEncryptionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of AEADEncryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of AEADEncryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlreadyFinalized": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions.AlreadyFinalized", "kind": "Gdef"}, "AlreadyUpdated": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions.AlreadyUpdated", "kind": "Gdef"}, "Cipher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher", "name": "Cipher", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "id": 1, "name": "Mode", "namespace": "cryptography.hazmat.primitives.ciphers.base.Cipher", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base.Cipher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "algorithm", "mode", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "algorithm", "mode", "backend"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "id": 1, "name": "Mode", "namespace": "cryptography.hazmat.primitives.ciphers.base.Cipher", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}, "cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "id": 1, "name": "Mode", "namespace": "cryptography.hazmat.primitives.ciphers.base.Cipher", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Cipher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "encrypt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher._wrap_ctx", "name": "_wrap_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "encrypt"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "id": 1, "name": "Mode", "namespace": "cryptography.hazmat.primitives.ciphers.base.Cipher", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}, "cryptography.hazmat.backends.openssl.ciphers._CipherContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_ctx of Cipher", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.algorithm", "name": "algorithm", "type": "cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm"}}, "decryptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.decryptor", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.decryptor", "name": "decryptor", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.decryptor", "name": "decryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.decryptor", "name": "decryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.decryptor", "name": "decryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.decryptor", "name": "decryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "encryptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.encryptor", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.encryptor", "name": "encryptor", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.encryptor", "name": "encryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.encryptor", "name": "encryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.encryptor", "name": "encryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.encryptor", "name": "encryptor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryptor of Cipher", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.mode", "name": "mode", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "id": 1, "name": "Mode", "namespace": "cryptography.hazmat.primitives.ciphers.base.Cipher", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Cipher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "id": 1, "name": "Mode", "namespace": "cryptography.hazmat.primitives.ciphers.base.Cipher", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["Mode"], "typeddict_type": null}}, "CipherAlgorithm": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "kind": "Gdef"}, "CipherContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["finalize", 1], ["update", 1], ["update_into", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "name": "CipherContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.finalize", "name": "finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.CipherContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize of CipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.finalize", "name": "finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.CipherContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize of CipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of CipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of CipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.update_into", "name": "update_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_into of CipherContext", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.update_into", "name": "update_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_into of CipherContext", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.CipherContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base.Mode", "name": "Mode", "upper_bound": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.Mode", {".class": "NoneType"}], "uses_pep604_syntax": false}, "values": [], "variance": 1}}, "NotYetFinalized": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions.NotYetFinalized", "kind": "Gdef"}, "_AEADCipherContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base.AEADCipherContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "name": "_AEADCipherContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "cryptography.hazmat.backends.openssl.ciphers._CipherContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AEADCipherContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_aad_bytes_processed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext._aad_bytes_processed", "name": "_aad_bytes_processed", "type": "builtins.int"}}, "_bytes_processed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext._bytes_processed", "name": "_bytes_processed", "type": "builtins.int"}}, "_check_limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext._check_limit", "name": "_check_limit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data_size"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_limit of _AEADCipherContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext._ctx", "name": "_ctx", "type": {".class": "UnionType", "items": ["cryptography.hazmat.backends.openssl.ciphers._CipherContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext._tag", "name": "_tag", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_updated": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext._updated", "name": "_updated", "type": "builtins.bool"}}, "authenticate_additional_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext.authenticate_additional_data", "name": "authenticate_additional_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authenticate_additional_data of _AEADCipherContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext.finalize", "name": "finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize of _AEADCipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of _AEADCipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext.update_into", "name": "update_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_into of _AEADCipherContext", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AEADDecryptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext", "name": "_AEADDecryptionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext", "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "finalize_with_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext.finalize_with_tag", "name": "finalize_with_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_with_tag of _AEADDecryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base._AEADDecryptionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AEADEncryptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext", "name": "_AEADEncryptionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext", "cryptography.hazmat.primitives.ciphers.base._AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADCipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of _AEADEncryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of _AEADEncryptionContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base._AEADEncryptionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BackendCipherContext": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.backends.openssl.ciphers._CipherContext", "kind": "Gdef"}, "_CIPHER_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.ciphers.base._CIPHER_TYPE", "line": 154, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.ModeWithNonce", "cryptography.hazmat.primitives.ciphers.modes.ModeWithTweak", {".class": "NoneType"}, "cryptography.hazmat.primitives.ciphers.modes.ECB", "cryptography.hazmat.primitives.ciphers.modes.ModeWithInitializationVector"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}}}, "_CipherContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.ciphers.base.CipherContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext", "name": "_CipherContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.base", "mro": ["cryptography.hazmat.primitives.ciphers.base._CipherContext", "cryptography.hazmat.primitives.ciphers.base.CipherContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._CipherContext", "cryptography.hazmat.backends.openssl.ciphers._CipherContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _CipherContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext._ctx", "name": "_ctx", "type": {".class": "UnionType", "items": ["cryptography.hazmat.backends.openssl.ciphers._CipherContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext.finalize", "name": "finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._CipherContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize of _CipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._CipherContext", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of _CipherContext", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext.update_into", "name": "update_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "buf"], "arg_types": ["cryptography.hazmat.primitives.ciphers.base._CipherContext", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_into of _CipherContext", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.base._CipherContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.base._CipherContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "modes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers.modes", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py"}