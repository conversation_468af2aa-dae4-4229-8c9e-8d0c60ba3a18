{"data_mtime": 1753887700, "dep_lines": [8, 12, 31, 32, 33, 3, 4, 5, 6, 7, 9, 10, 137, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["ollama._types", "llm_proxy_server.models", "llm_proxy_server.config", "llm_proxy_server.load_balancer", "llm_proxy_server.monitoring", "json", "time", "pathlib", "typing", "ollama", "structlog", "<PERSON><PERSON><PERSON>", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "datetime", "fastapi.exceptions", "io", "json.decoder", "ollama._client", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.types", "starlette", "starlette.exceptions"], "hash": "0f86447061e487a8eb3da130c287454f07b4bc22", "id": "llm_proxy_server.proxy_manager", "ignore_all": true, "interface_hash": "1f017fa9bec1ef8f640c7ee4cd28f2422a50e208", "mtime": 1753890022, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\llm_proxy_server\\proxy_manager.py", "plugin_data": null, "size": 43687, "suppressed": [], "version_id": "1.15.0"}