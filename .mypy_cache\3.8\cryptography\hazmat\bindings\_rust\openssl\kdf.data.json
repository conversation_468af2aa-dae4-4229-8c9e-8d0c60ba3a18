{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl.kdf", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "HashAlgorithm": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes.HashAlgorithm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "derive_pbkdf2_hmac": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["key_material", "algorithm", "salt", "iterations", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.derive_pbkdf2_hmac", "name": "derive_pbkdf2_hmac", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["key_material", "algorithm", "salt", "iterations", "length"], "arg_types": ["builtins.bytes", "cryptography.hazmat.primitives.hashes.HashAlgorithm", "builtins.bytes", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "derive_pbkdf2_hmac", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "derive_scrypt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["key_material", "salt", "n", "r", "p", "max_mem", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.kdf.derive_scrypt", "name": "derive_scrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["key_material", "salt", "n", "r", "p", "max_mem", "length"], "arg_types": ["builtins.bytes", "builtins.bytes", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "derive_scrypt", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\kdf.pyi"}