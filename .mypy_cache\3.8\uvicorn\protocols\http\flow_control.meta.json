{"data_mtime": 1753889305, "dep_lines": [3, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["uvicorn._types", "asyncio", "builtins", "_frozen_importlib", "abc", "asyncio.events", "asyncio.locks", "asyncio.transports", "typing"], "hash": "548021705c40353ded7f632fdfd2171bf2629069", "id": "uvicorn.protocols.http.flow_control", "ignore_all": true, "interface_hash": "cd9c46ee40ec73f301d7b02acdc776b14d8feb51", "mtime": 1750470764, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\protocols\\http\\flow_control.py", "plugin_data": null, "size": 1701, "suppressed": [], "version_id": "1.15.0"}