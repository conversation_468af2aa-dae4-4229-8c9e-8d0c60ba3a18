{"data_mtime": 1753889303, "dep_lines": [10, 11, 12, 10, 12, 9, 9, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 20, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.backends.openssl.aead", "cryptography.hazmat.backends.openssl.backend", "cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.backends.openssl", "cryptography.hazmat.bindings._rust", "cryptography.exceptions", "cryptography.utils", "__future__", "os", "cryptography", "builtins", "_frozen_importlib", "_typeshed", "abc", "cryptography.hazmat.backends", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust.exceptions", "cryptography.hazmat.bindings._rust.openssl.aead", "typing", "typing_extensions"], "hash": "cfbed8ce286584782a30c3503a911e7294e5d1d8", "id": "cryptography.hazmat.primitives.ciphers.aead", "ignore_all": true, "interface_hash": "92ef5691232c1bfa9564d0556d2c5b009432481d", "mtime": 1708667824, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py", "plugin_data": null, "size": 5540, "suppressed": [], "version_id": "1.15.0"}