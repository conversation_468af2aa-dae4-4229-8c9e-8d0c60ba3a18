{".class": "MypyFile", "_fullname": "uvicorn.protocols.websockets.wsproto_impl", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGI3Application": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGI3Application", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "ClientDisconnected": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.ClientDisconnected", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.Config", "kind": "Gdef"}, "ConnectionState": {".class": "SymbolTableNode", "cross_ref": "wsproto.connection.ConnectionState", "kind": "Gdef"}, "ConnectionType": {".class": "SymbolTableNode", "cross_ref": "wsproto.connection.ConnectionType", "kind": "Gdef"}, "Extension": {".class": "SymbolTableNode", "cross_ref": "wsproto.extensions.Extension", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.LocalProtocolError", "kind": "Gdef"}, "PerMessageDeflate": {".class": "SymbolTableNode", "cross_ref": "wsproto.extensions.PerMessageDeflate", "kind": "Gdef"}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.RemoteProtocolError", "kind": "Gdef"}, "ServerState": {".class": "SymbolTableNode", "cross_ref": "uvicorn.server.ServerState", "kind": "Gdef"}, "TRACE_LOG_LEVEL": {".class": "SymbolTableNode", "cross_ref": "uvicorn.logging.TRACE_LOG_LEVEL", "kind": "Gdef"}, "WSProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "name": "WSProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.websockets.wsproto_impl", "mro": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "uvicorn.config.Config", "uvicorn.server.ServerState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.app_state", "name": "app_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.bytes", "name": "bytes", "type": "builtins.bytes"}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.client", "name": "client", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "close_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.close_sent", "name": "close_sent", "type": "builtins.bool"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.config", "name": "config", "type": "uvicorn.config.Config"}}, "conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.conn", "name": "conn", "type": "wsproto.WSConnection"}}, "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_lost of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "asyncio.transports.Transport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.connections", "name": "connections", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.server.Protocols"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.default_headers", "name": "default_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eof_received of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handle_bytes", "name": "handle_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "wsproto.events.BytesMessage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_bytes of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handle_close", "name": "handle_close", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "wsproto.events.CloseConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_close of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handle_connect", "name": "handle_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "wsproto.events.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_connect of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handle_events", "name": "handle_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_events of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handle_ping", "name": "handle_ping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "wsproto.events.Ping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_ping of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handle_text", "name": "handle_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "wsproto.events.TextMessage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_text of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handshake_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.handshake_complete", "name": "handshake_complete", "type": "builtins.bool"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.logger", "name": "logger", "type": "logging.Logger"}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.loop", "name": "loop", "type": "asyncio.events.AbstractEventLoop"}}, "on_task_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.on_task_complete", "name": "on_task_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_task_complete of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pause_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.pause_writing", "name": "pause_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pause_writing of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.queue", "name": "queue", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketEvent"}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}}, "read_paused": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.read_paused", "name": "read_paused", "type": "builtins.bool"}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of WSProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response_started": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.response_started", "name": "response_started", "type": "builtins.bool"}}, "resume_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.resume_writing", "name": "resume_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resume_writing of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "root_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.root_path", "name": "root_path", "type": "builtins.str"}}, "run_asgi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.run_asgi", "name": "run_asgi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_asgi of WSProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.scheme", "name": "scheme", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ws"}], "uses_pep604_syntax": false}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketScope"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of WSProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_500_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.send_500_response", "name": "send_500_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_500_response of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.server", "name": "server", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.wsproto_impl.WSProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of WSProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.tasks", "name": "tasks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.text", "name": "text", "type": "builtins.str"}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "writable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.writable", "name": "writable", "type": "asyncio.locks.Event"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.websockets.wsproto_impl.WSProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WebSocketAcceptEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketAcceptEvent", "kind": "Gdef"}, "WebSocketCloseEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketCloseEvent", "kind": "Gdef"}, "WebSocketEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketEvent", "kind": "Gdef"}, "WebSocketResponseBodyEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketResponseBodyEvent", "kind": "Gdef"}, "WebSocketResponseStartEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketResponseStartEvent", "kind": "Gdef"}, "WebSocketScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketScope", "kind": "Gdef"}, "WebSocketSendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketSendEvent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.wsproto_impl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "events": {".class": "SymbolTableNode", "cross_ref": "wsproto.events", "kind": "Gdef"}, "get_local_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_local_addr", "kind": "Gdef"}, "get_path_with_query_string": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_path_with_query_string", "kind": "Gdef"}, "get_remote_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_remote_addr", "kind": "Gdef"}, "is_ssl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.is_ssl", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "wsproto": {".class": "SymbolTableNode", "cross_ref": "wsproto", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py"}