{".class": "MypyFile", "_fullname": "uvicorn.protocols.http.h11_impl", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGI3Application": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGI3Application", "kind": "Gdef"}, "ASGIReceiveEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveEvent", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CLOSE_HEADER": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.CLOSE_HEADER", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.Config", "kind": "Gdef"}, "DEFAULT_MAX_INCOMPLETE_EVENT_SIZE": {".class": "SymbolTableNode", "cross_ref": "h11._connection.DEFAULT_MAX_INCOMPLETE_EVENT_SIZE", "kind": "Gdef"}, "FlowControl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.FlowControl", "kind": "Gdef"}, "H11Protocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol", "name": "H11Protocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.http.h11_impl", "mro": ["uvicorn.protocols.http.h11_impl.H11Protocol", "asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol", "uvicorn.config.Config", "uvicorn.server.ServerState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol._get_upgrade", "name": "_get_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_upgrade of H11Protocol", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol._should_upgrade", "name": "_should_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_upgrade of H11Protocol", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_upgrade_to_ws": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol._should_upgrade_to_ws", "name": "_should_upgrade_to_ws", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_upgrade_to_ws of H11Protocol", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unset_keepalive_if_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol._unset_keepalive_if_required", "name": "_unset_keepalive_if_required", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unset_keepalive_if_required of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unsupported_upgrade_warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol._unsupported_upgrade_warning", "name": "_unsupported_upgrade_warning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unsupported_upgrade_warning of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.access_log", "name": "access_log", "type": "builtins.bool"}}, "access_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.access_logger", "name": "access_logger", "type": "logging.Logger"}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.app", "name": "app", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "app_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.app_state", "name": "app_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.client", "name": "client", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.config", "name": "config", "type": "uvicorn.config.Config"}}, "conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.conn", "name": "conn", "type": "h11._connection.Connection"}}, "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_lost of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol", "asyncio.transports.Transport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_made of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.connections", "name": "connections", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.server.Protocols"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "cycle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.cycle", "name": "cycle", "type": "uvicorn.protocols.http.h11_impl.RequestResponseCycle"}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_received of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eof_received of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.flow", "name": "flow", "type": "uvicorn.protocols.http.flow_control.FlowControl"}}, "handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.handle_events", "name": "handle_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_events of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_websocket_upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.handle_websocket_upgrade", "name": "handle_websocket_upgrade", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol", "h11._events.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_websocket_upgrade of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "limit_concurrency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.limit_concurrency", "name": "limit_concurrency", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.logger", "name": "logger", "type": "logging.Logger"}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.loop", "name": "loop", "type": "asyncio.events.AbstractEventLoop"}}, "on_response_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.on_response_complete", "name": "on_response_complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_response_complete of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pause_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.pause_writing", "name": "pause_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pause_writing of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resume_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.resume_writing", "name": "resume_writing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resume_writing of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "root_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.root_path", "name": "root_path", "type": "builtins.str"}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.scheme", "name": "scheme", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "http"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "https"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}}}, "send_400_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.send_400_response", "name": "send_400_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_400_response of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.server", "name": "server", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.server_state", "name": "server_state", "type": "uvicorn.server.ServerState"}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.tasks", "name": "tasks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "timeout_keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.timeout_keep_alive", "name": "timeout_keep_alive", "type": "builtins.int"}}, "timeout_keep_alive_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.timeout_keep_alive_handler", "name": "timeout_keep_alive_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.H11Protocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout_keep_alive_handler of H11Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout_keep_alive_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.timeout_keep_alive_task", "name": "timeout_keep_alive_task", "type": {".class": "UnionType", "items": ["asyncio.events.TimerHandle", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "ws_protocol_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.ws_protocol_class", "name": "ws_protocol_class", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "asyncio.protocols.Protocol"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.http.h11_impl.H11Protocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.http.h11_impl.H11Protocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HIGH_WATER_LIMIT": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.HIGH_WATER_LIMIT", "kind": "Gdef"}, "HTTPRequestEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPRequestEvent", "kind": "Gdef"}, "HTTPResponseBodyEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPResponseBodyEvent", "kind": "Gdef"}, "HTTPResponseStartEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPResponseStartEvent", "kind": "Gdef"}, "HTTPScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.HTTPScope", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "RequestResponseCycle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle", "name": "RequestResponseCycle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.http.h11_impl", "mro": ["uvicorn.protocols.http.h11_impl.RequestResponseCycle", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scope", "conn", "transport", "flow", "logger", "access_logger", "access_log", "default_headers", "message_event", "on_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "scope", "conn", "transport", "flow", "logger", "access_logger", "access_log", "default_headers", "message_event", "on_response"], "arg_types": ["uvicorn.protocols.http.h11_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}, "h11._connection.Connection", "asyncio.transports.Transport", "uvicorn.protocols.http.flow_control.FlowControl", "logging.Logger", "logging.Logger", "builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "asyncio.locks.Event", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestResponseCycle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.access_log", "name": "access_log", "type": "builtins.bool"}}, "access_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.access_logger", "name": "access_logger", "type": "logging.Logger"}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.body", "name": "body", "type": "builtins.bytes"}}, "conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.conn", "name": "conn", "type": "h11._connection.Connection"}}, "default_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.default_headers", "name": "default_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "disconnected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.disconnected", "name": "disconnected", "type": "builtins.bool"}}, "flow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.flow", "name": "flow", "type": "uvicorn.protocols.http.flow_control.FlowControl"}}, "keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.keep_alive", "name": "keep_alive", "type": "builtins.bool"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.logger", "name": "logger", "type": "logging.Logger"}}, "message_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.message_event", "name": "message_event", "type": "asyncio.locks.Event"}}, "more_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.more_body", "name": "more_body", "type": "builtins.bool"}}, "on_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.RequestResponseCycle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.response_complete", "name": "response_complete", "type": "builtins.bool"}}, "response_started": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.response_started", "name": "response_started", "type": "builtins.bool"}}, "run_asgi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.run_asgi", "name": "run_asgi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["uvicorn.protocols.http.h11_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGI3Application"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_asgi of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.scope", "name": "scope", "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["uvicorn.protocols.http.h11_impl.RequestResponseCycle", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_500_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.send_500_response", "name": "send_500_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.http.h11_impl.RequestResponseCycle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_500_response of RequestResponseCycle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "waiting_for_100_continue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.waiting_for_100_continue", "name": "waiting_for_100_continue", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.http.h11_impl.RequestResponseCycle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.http.h11_impl.RequestResponseCycle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STATUS_PHRASES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.protocols.http.h11_impl.STATUS_PHRASES", "name": "STATUS_PHRASES", "type": {".class": "Instance", "args": ["builtins.int", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ServerState": {".class": "SymbolTableNode", "cross_ref": "uvicorn.server.ServerState", "kind": "Gdef"}, "TRACE_LOG_LEVEL": {".class": "SymbolTableNode", "cross_ref": "uvicorn.logging.TRACE_LOG_LEVEL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.h11_impl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.h11_impl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.h11_impl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.h11_impl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.h11_impl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.http.h11_impl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_status_phrase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["status_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.http.h11_impl._get_status_phrase", "name": "_get_status_phrase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["status_code"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_status_phrase", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_client_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_client_addr", "kind": "Gdef"}, "get_local_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_local_addr", "kind": "Gdef"}, "get_path_with_query_string": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_path_with_query_string", "kind": "Gdef"}, "get_remote_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_remote_addr", "kind": "Gdef"}, "h11": {".class": "SymbolTableNode", "cross_ref": "h11", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "is_ssl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.is_ssl", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "service_unavailable": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.http.flow_control.service_unavailable", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py"}