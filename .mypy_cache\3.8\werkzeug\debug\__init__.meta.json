{"data_mtime": 1753889305, "dep_lines": [25, 26, 27, 28, 16, 20, 21, 22, 23, 24, 33, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.wrappers.request", "werkzeug.wrappers.response", "werkzeug.debug.console", "werkzeug.debug.tbtools", "os.path", "werkzeug._internal", "werkzeug.exceptions", "werkzeug.http", "werkzeug.security", "werkzeug.utils", "_typeshed.wsgi", "__future__", "getpass", "<PERSON><PERSON><PERSON>", "json", "os", "pkgu<PERSON>", "re", "sys", "time", "typing", "uuid", "contextlib", "io", "itertools", "zlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "enum", "http", "json.encoder", "traceback", "typing_extensions", "werkzeug.datastructures", "werkzeug.datastructures.structures", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers"], "hash": "f952cac5594f16ba580180f39fd79a47a499adbe", "id": "werkzeug.debug", "ignore_all": true, "interface_hash": "5eb861149679137709e2989e3f3742ab3a90b205", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\debug\\__init__.py", "plugin_data": null, "size": 18760, "suppressed": [], "version_id": "1.15.0"}