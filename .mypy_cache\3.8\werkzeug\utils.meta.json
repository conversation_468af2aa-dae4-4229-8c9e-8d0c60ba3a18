{"data_mtime": 1753889304, "dep_lines": [29, 30, 13, 18, 21, 22, 24, 25, 28, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.wrappers.request", "werkzeug.wrappers.response", "urllib.parse", "werkzeug._internal", "werkzeug.datastructures", "werkzeug.exceptions", "werkzeug.security", "werkzeug.wsgi", "_typeshed.wsgi", "__future__", "io", "mimetypes", "os", "pkgu<PERSON>", "re", "sys", "typing", "unicodedata", "datetime", "time", "zlib", "markupsafe", "builtins", "_frozen_importlib", "abc", "enum", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers"], "hash": "1321b047b581141136639daee451f656d39686da", "id": "werkzeug.utils", "ignore_all": true, "interface_hash": "973fad077f6379af4e54d720bd89f39bdae2bd9d", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\utils.py", "plugin_data": null, "size": 24654, "suppressed": [], "version_id": "1.15.0"}