{"data_mtime": 1753889303, "dep_lines": [16, 16, 17, 16, 15, 5, 7, 8, 9, 10, 11, 12, 14, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust._openssl", "cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.bindings.openssl._conditional", "cryptography.hazmat.bindings._rust", "cryptography.exceptions", "__future__", "os", "sys", "threading", "types", "typing", "warnings", "cryptography", "builtins", "_frozen_importlib", "_thread", "abc"], "hash": "a5754861609329fbe5f00291f43843575feaa13b", "id": "cryptography.hazmat.bindings.openssl.binding", "ignore_all": true, "interface_hash": "e048125f73dd352ef407212eab7e00c135d8050d", "mtime": 1708667824, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py", "plugin_data": null, "size": 4935, "suppressed": [], "version_id": "1.15.0"}