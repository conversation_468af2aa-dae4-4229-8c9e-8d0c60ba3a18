{".class": "MypyFile", "_fullname": "werkzeug.routing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.AnyConverter", "kind": "Gdef"}, "BaseConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.BaseConverter", "kind": "Gdef"}, "BuildError": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.BuildError", "kind": "Gdef"}, "EndpointPrefix": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.EndpointPrefix", "kind": "Gdef"}, "FloatConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.FloatConverter", "kind": "Gdef"}, "IntegerConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.IntegerConverter", "kind": "Gdef"}, "Map": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.map.Map", "kind": "Gdef"}, "MapAdapter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.map.MapAdapter", "kind": "Gdef"}, "NoMatch": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.NoMatch", "kind": "Gdef"}, "PathConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.PathConverter", "kind": "Gdef"}, "RequestAliasRedirect": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestAliasRedirect", "kind": "Gdef"}, "RequestPath": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestPath", "kind": "Gdef"}, "RequestRedirect": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestRedirect", "kind": "Gdef"}, "RoutingException": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RoutingException", "kind": "Gdef"}, "Rule": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.Rule", "kind": "Gdef"}, "RuleFactory": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.RuleFactory", "kind": "Gdef"}, "RuleTemplate": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.RuleTemplate", "kind": "Gdef"}, "RuleTemplateFactory": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.RuleTemplateFactory", "kind": "Gdef"}, "StateMachineMatcher": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.matcher.StateMachineMatcher", "kind": "Gdef"}, "Subdomain": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.Subdomain", "kind": "Gdef"}, "Submount": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.Submount", "kind": "Gdef"}, "UUIDConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.UUIDConverter", "kind": "Gdef"}, "UnicodeConverter": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.UnicodeConverter", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.converters.ValidationError", "kind": "Gdef"}, "WebsocketMismatch": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.WebsocketMismatch", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "parse_converter_args": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.rules.parse_converter_args", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\routing\\__init__.py"}