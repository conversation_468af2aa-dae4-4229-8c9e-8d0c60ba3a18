{".class": "MypyFile", "_fullname": "werkzeug.routing.converters", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.BaseConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.AnyConverter", "name": "AnyConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.AnyConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.AnyConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "map", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.AnyConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "map", "items"], "arg_types": ["werkzeug.routing.converters.AnyConverter", "werkzeug.routing.map.Map", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AnyConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.converters.AnyConverter.items", "name": "items", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "to_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.AnyConverter.to_url", "name": "to_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.AnyConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_url of AnyConverter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.AnyConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.AnyConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.BaseConverter", "name": "BaseConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.BaseConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "map", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.BaseConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "map", "args", "kwargs"], "arg_types": ["werkzeug.routing.converters.BaseConverter", "werkzeug.routing.map.Map", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "werkzeug.routing.converters.BaseConverter.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "werkzeug.routing.converters.BaseConverter"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of BaseConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.converters.BaseConverter.map", "name": "map", "type": "werkzeug.routing.map.Map"}}, "part_isolating": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.BaseConverter.part_isolating", "name": "part_isolating", "type": "builtins.bool"}}, "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.BaseConverter.regex", "name": "regex", "type": "builtins.str"}}, "to_python": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.BaseConverter.to_python", "name": "to_python", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.BaseConverter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_python of BaseConverter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.BaseConverter.to_url", "name": "to_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.BaseConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_url of BaseConverter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.BaseConverter.weight", "name": "weight", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.BaseConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.BaseConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULT_CONVERTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "werkzeug.routing.converters.DEFAULT_CONVERTERS", "name": "DEFAULT_CONVERTERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "werkzeug.routing.converters.BaseConverter"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "FloatConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.NumberConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.FloatConverter", "name": "FloatConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.FloatConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.FloatConverter", "werkzeug.routing.converters.NumberConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "map", "min", "max", "signed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.FloatConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "map", "min", "max", "signed"], "arg_types": ["werkzeug.routing.converters.FloatConverter", "werkzeug.routing.map.Map", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FloatConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "num_convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.FloatConverter.num_convert", "name": "num_convert", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.ConvertibleToFloat"}], "bound_args": ["builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.FloatConverter.regex", "name": "regex", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.FloatConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.FloatConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntegerConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.NumberConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.IntegerConverter", "name": "IntegerConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.IntegerConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.IntegerConverter", "werkzeug.routing.converters.NumberConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.IntegerConverter.regex", "name": "regex", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.IntegerConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.IntegerConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Map": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.map.Map", "kind": "Gdef"}, "NumberConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.BaseConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.NumberConverter", "name": "NumberConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.NumberConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.NumberConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "map", "fixed_digits", "min", "max", "signed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.NumberConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "map", "fixed_digits", "min", "max", "signed"], "arg_types": ["werkzeug.routing.converters.NumberConverter", "werkzeug.routing.map.Map", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NumberConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fixed_digits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.converters.NumberConverter.fixed_digits", "name": "fixed_digits", "type": "builtins.int"}}, "max": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.converters.NumberConverter.max", "name": "max", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "min": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.converters.NumberConverter.min", "name": "min", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "num_convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "werkzeug.routing.converters.NumberConverter.num_convert", "name": "num_convert", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.routing.converters.NumberConverter.signed", "name": "signed", "type": "builtins.bool"}}, "signed_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "werkzeug.routing.converters.NumberConverter.signed_regex", "name": "signed_regex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.routing.converters.NumberConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signed_regex of NumberConverter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "werkzeug.routing.converters.NumberConverter.signed_regex", "name": "signed_regex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.routing.converters.NumberConverter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signed_regex of NumberConverter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_python": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.NumberConverter.to_python", "name": "to_python", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.NumberConverter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_python of NumberConverter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.NumberConverter.to_url", "name": "to_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.NumberConverter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_url of NumberConverter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.NumberConverter.weight", "name": "weight", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.NumberConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.NumberConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PathConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.BaseConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.PathConverter", "name": "PathConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.PathConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.PathConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "part_isolating": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.PathConverter.part_isolating", "name": "part_isolating", "type": "builtins.bool"}}, "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.PathConverter.regex", "name": "regex", "type": "builtins.str"}}, "weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.PathConverter.weight", "name": "weight", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.PathConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.PathConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UUIDConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.BaseConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.UUIDConverter", "name": "UUIDConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.UUIDConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.UUIDConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.routing.converters.UUIDConverter.regex", "name": "regex", "type": "builtins.str"}}, "to_python": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.UUIDConverter.to_python", "name": "to_python", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.UUIDConverter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_python of UUIDConverter", "ret_type": "uuid.UUID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.UUIDConverter.to_url", "name": "to_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.routing.converters.UUIDConverter", "uuid.UUID"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_url of UUIDConverter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.UUIDConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.UUIDConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnicodeConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.routing.converters.BaseConverter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.UnicodeConverter", "name": "UnicodeConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.UnicodeConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.UnicodeConverter", "werkzeug.routing.converters.BaseConverter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "map", "minlength", "maxlength", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.UnicodeConverter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "map", "minlength", "maxlength", "length"], "arg_types": ["werkzeug.routing.converters.UnicodeConverter", "werkzeug.routing.map.Map", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnicodeConverter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.UnicodeConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.UnicodeConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.routing.converters.ValidationError", "name": "ValidationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.routing.converters.ValidationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.routing.converters", "mro": ["werkzeug.routing.converters.ValidationError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.routing.converters.ValidationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.routing.converters.ValidationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.converters.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.converters.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.converters.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.converters.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.converters.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.routing.converters.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\routing\\converters.py"}