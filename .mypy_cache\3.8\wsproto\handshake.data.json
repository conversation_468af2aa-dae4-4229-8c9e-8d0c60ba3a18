{".class": "MypyFile", "_fullname": "wsproto.handshake", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AcceptConnection": {".class": "SymbolTableNode", "cross_ref": "wsproto.events.AcceptConnection", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "wsproto.connection.Connection", "kind": "Gdef"}, "ConnectionState": {".class": "SymbolTableNode", "cross_ref": "wsproto.connection.ConnectionState", "kind": "Gdef"}, "ConnectionType": {".class": "SymbolTableNode", "cross_ref": "wsproto.connection.ConnectionType", "kind": "Gdef"}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "wsproto.events.Event", "kind": "Gdef"}, "Extension": {".class": "SymbolTableNode", "cross_ref": "wsproto.extensions.Extension", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "H11Handshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsproto.handshake.H11Handshake", "name": "H11Handshake", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsproto.handshake", "mro": ["wsproto.handshake.H11Handshake", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection_type"], "arg_types": ["wsproto.handshake.H11Handshake", "wsproto.connection.ConnectionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of H11Handshake", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["wsproto.handshake.H11Handshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of H11Handshake", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake._accept", "name": "_accept", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["wsproto.handshake.H11Handshake", "wsproto.events.AcceptConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept of H11Handshake", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "wsproto.handshake.H11Handshake._connection", "name": "_connection", "type": {".class": "UnionType", "items": ["wsproto.connection.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_establish_client_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake._establish_client_connection", "name": "_establish_client_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["wsproto.handshake.H11Handshake", "h11._events.InformationalResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_establish_client_connection of H11Handshake", "ret_type": "wsproto.events.AcceptConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_events": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "wsproto.handshake.H11Handshake._events", "name": "_events", "type": {".class": "Instance", "args": ["wsproto.events.Event"], "extra_attrs": null, "type_ref": "collections.deque"}}}, "_h11_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "wsproto.handshake.H11Handshake._h11_connection", "name": "_h11_connection", "type": "h11._connection.Connection"}}, "_initiate_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake._initiate_connection", "name": "_initiate_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["wsproto.handshake.H11Handshake", "wsproto.events.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_initiate_connection of H11Handshake", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initiating_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "wsproto.handshake.H11Handshake._initiating_request", "name": "_initiating_request", "type": {".class": "UnionType", "items": ["wsproto.events.Request", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_nonce": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "wsproto.handshake.H11Handshake._nonce", "name": "_nonce", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_process_connection_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake._process_connection_request", "name": "_process_connection_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["wsproto.handshake.H11Handshake", "h11._events.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_connection_request of H11Handshake", "ret_type": "wsproto.events.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake._reject", "name": "_reject", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["wsproto.handshake.H11Handshake", "wsproto.events.RejectConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reject of H11Handshake", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_send_reject_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake._send_reject_data", "name": "_send_reject_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["wsproto.handshake.H11Handshake", "wsproto.events.RejectData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_send_reject_data of H11Handshake", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "wsproto.handshake.H11Handshake._state", "name": "_state", "type": "wsproto.connection.ConnectionState"}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "wsproto.handshake.H11Handshake.client", "name": "client", "type": "builtins.bool"}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "wsproto.handshake.H11Handshake.connection", "name": "connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsproto.handshake.H11Handshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection of H11Handshake", "ret_type": {".class": "UnionType", "items": ["wsproto.connection.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "wsproto.handshake.H11Handshake.connection", "name": "connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsproto.handshake.H11Handshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection of H11Handshake", "ret_type": {".class": "UnionType", "items": ["wsproto.connection.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake.events", "name": "events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsproto.handshake.H11Handshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "events of H11Handshake", "ret_type": {".class": "Instance", "args": ["wsproto.events.Event", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initiate_upgrade_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake.initiate_upgrade_connection", "name": "initiate_upgrade_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "path"], "arg_types": ["wsproto.handshake.H11Handshake", {".class": "TypeAliasType", "args": [], "type_ref": "wsproto.typing.Headers"}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initiate_upgrade_connection of H11Handshake", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake.receive_data", "name": "receive_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["wsproto.handshake.H11Handshake", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_data of H11Handshake", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.H11Handshake.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["wsproto.handshake.H11Handshake", "wsproto.events.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of H11Handshake", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "wsproto.handshake.H11Handshake.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsproto.handshake.H11Handshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of H11Handshake", "ret_type": "wsproto.connection.ConnectionState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "wsproto.handshake.H11Handshake.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsproto.handshake.H11Handshake"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of H11Handshake", "ret_type": "wsproto.connection.ConnectionState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsproto.handshake.H11Handshake.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsproto.handshake.H11Handshake", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "wsproto.typing.Headers", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.LocalProtocolError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RejectConnection": {".class": "SymbolTableNode", "cross_ref": "wsproto.events.RejectConnection", "kind": "Gdef"}, "RejectData": {".class": "SymbolTableNode", "cross_ref": "wsproto.events.RejectData", "kind": "Gdef"}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.RemoteProtocolError", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "wsproto.events.Request", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WEBSOCKET_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wsproto.handshake.WEBSOCKET_VERSION", "name": "WEBSOCKET_VERSION", "type": "builtins.bytes"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.handshake.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.handshake.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.handshake.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.handshake.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.handshake.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.handshake.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "client_extensions_handshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["accepted", "supported"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.client_extensions_handshake", "name": "client_extensions_handshake", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["accepted", "supported"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["wsproto.extensions.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "client_extensions_handshake", "ret_type": {".class": "Instance", "args": ["wsproto.extensions.Extension"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "generate_accept_token": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.generate_accept_token", "kind": "Gdef"}, "generate_nonce": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.generate_nonce", "kind": "Gdef"}, "h11": {".class": "SymbolTableNode", "cross_ref": "h11", "kind": "Gdef"}, "normed_header_dict": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.normed_header_dict", "kind": "Gdef"}, "server_extensions_handshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["requested", "supported"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.handshake.server_extensions_handshake", "name": "server_extensions_handshake", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["requested", "supported"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["wsproto.extensions.Extension"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "server_extensions_handshake", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_comma_header": {".class": "SymbolTableNode", "cross_ref": "wsproto.utilities.split_comma_header", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\wsproto\\handshake.py"}