{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives.asymmetric.dh", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DHParameterNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameterNumbers", "line": 17, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.dh.DHParameterNumbers"}}, "DHParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["generate_private_key", 1], ["parameter_bytes", 1], ["parameter_numbers", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "name": "DHParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.dh", "mro": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "builtins.object"], "names": {".class": "SymbolTable", "generate_private_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.generate_private_key", "name": "generate_private_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_private_key of DHParameters", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.generate_private_key", "name": "generate_private_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_private_key of DHParameters", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameter_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.parameter_bytes", "name": "parameter_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.ParameterFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_bytes of DHParameters", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.parameter_bytes", "name": "parameter_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.ParameterFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_bytes of DHParameters", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameter_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.parameter_numbers", "name": "parameter_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_numbers of DHParameters", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dh.DHParameterNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.parameter_numbers", "name": "parameter_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHParameters"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_numbers of DHParameters", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dh.DHParameterNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DHParametersWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHParametersWithSerialization", "line": 44, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters"}}, "DHPrivateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["exchange", 1], ["key_size", 1], ["parameters", 1], ["private_bytes", 1], ["private_numbers", 1], ["public_key", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "name": "DHPrivateKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.dh", "mro": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "builtins.object"], "names": {".class": "SymbolTable", "exchange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "peer_public_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.exchange", "name": "exchange", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "peer_public_key"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exchange of DHPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.exchange", "name": "exchange", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "peer_public_key"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exchange of DHPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of DHPrivateKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of DHPrivateKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameters of DHPrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameters of DHPrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.private_bytes", "name": "private_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_bytes of DHPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.private_bytes", "name": "private_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_bytes of DHPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.private_numbers", "name": "private_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_numbers of DHPrivateKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dh.DHPrivateNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.private_numbers", "name": "private_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_numbers of DHPrivateKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dh.DHPrivateNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of DHPrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of DHPrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DHPrivateKeyWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKeyWithSerialization", "line": 134, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateKey"}}, "DHPrivateNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPrivateNumbers", "line": 15, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.dh.DHPrivateNumbers"}}, "DHPublicKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__eq__", 1], ["key_size", 1], ["parameters", 1], ["public_bytes", 1], ["public_numbers", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "name": "DHPublicKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.dh", "mro": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of DHPublicKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of DHPublicKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of DHPublicKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of DHPublicKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameters of DHPublicKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameters of DHPublicKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PublicFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of DHPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PublicFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of DHPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of DHPublicKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dh.DHPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of DHPublicKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dh.DHPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DHPublicKeyWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKeyWithSerialization", "line": 85, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicKey"}}, "DHPublicNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.dh.DHPublicNumbers", "line": 16, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.dh.DHPublicNumbers"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_serialization": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "generate_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.dh.generate_parameters", "name": "generate_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["generator", "key_size", "backend"], "arg_types": ["builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.primitives.asymmetric.dh.DHParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rust_openssl": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py"}