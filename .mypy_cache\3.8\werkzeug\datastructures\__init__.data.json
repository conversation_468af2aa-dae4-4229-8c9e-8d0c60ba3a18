{".class": "MypyFile", "_fullname": "werkzeug.datastructures", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Accept": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.accept.Accept", "kind": "Gdef"}, "Authorization": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.auth.Authorization", "kind": "Gdef"}, "CallbackDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.CallbackDict", "kind": "Gdef"}, "CharsetAccept": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.accept.CharsetAccept", "kind": "Gdef"}, "CombinedMultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.CombinedMultiDict", "kind": "Gdef"}, "ContentRange": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.range.ContentRange", "kind": "Gdef"}, "ContentSecurityPolicy": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.csp.ContentSecurityPolicy", "kind": "Gdef"}, "ETags": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.etag.ETags", "kind": "Gdef"}, "EnvironHeaders": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.headers.EnvironHeaders", "kind": "Gdef"}, "FileMultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.file_storage.FileMultiDict", "kind": "Gdef"}, "FileStorage": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.file_storage.FileStorage", "kind": "Gdef"}, "HeaderSet": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.HeaderSet", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.headers.Headers", "kind": "Gdef"}, "IfRange": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.range.IfRange", "kind": "Gdef"}, "ImmutableDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.ImmutableDict", "kind": "Gdef"}, "ImmutableDictMixin": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.mixins.ImmutableDictMixin", "kind": "Gdef"}, "ImmutableHeadersMixin": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.mixins.ImmutableHeadersMixin", "kind": "Gdef"}, "ImmutableList": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.ImmutableList", "kind": "Gdef"}, "ImmutableListMixin": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.mixins.ImmutableListMixin", "kind": "Gdef"}, "ImmutableMultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.ImmutableMultiDict", "kind": "Gdef"}, "ImmutableMultiDictMixin": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.mixins.ImmutableMultiDictMixin", "kind": "Gdef"}, "ImmutableOrderedMultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.ImmutableOrderedMultiDict", "kind": "Gdef"}, "ImmutableTypeConversionDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.ImmutableTypeConversionDict", "kind": "Gdef"}, "LanguageAccept": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.accept.LanguageAccept", "kind": "Gdef"}, "MIMEAccept": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.accept.MIMEAccept", "kind": "Gdef"}, "MultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.MultiDict", "kind": "Gdef"}, "OrderedMultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.OrderedMultiDict", "kind": "Gdef"}, "Range": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.range.Range", "kind": "Gdef"}, "RequestCacheControl": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.cache_control.RequestCacheControl", "kind": "Gdef"}, "ResponseCacheControl": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.cache_control.ResponseCacheControl", "kind": "Gdef"}, "TypeConversionDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.TypeConversionDict", "kind": "Gdef"}, "UpdateDictMixin": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.mixins.UpdateDictMixin", "kind": "Gdef"}, "WWWAuthenticate": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.auth.WWWAuthenticate", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.datastructures.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "iter_multi_items": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.iter_multi_items", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\datastructures\\__init__.py"}