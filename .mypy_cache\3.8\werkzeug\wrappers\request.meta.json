{"data_mtime": 1753889304, "dep_lines": [19, 8, 9, 15, 17, 20, 22, 26, 150, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.request", "werkzeug._internal", "werkzeug.datastructures", "werkzeug.exceptions", "werkzeug.formparser", "werkzeug.utils", "werkzeug.wsgi", "_typeshed.wsgi", "werkzeug.test", "__future__", "functools", "json", "typing", "io", "builtins", "_frozen_importlib", "_typeshed", "abc", "json.decoder", "types", "werkzeug.datastructures.file_storage", "werkzeug.datastructures.headers", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.sansio"], "hash": "b02f63470671c0ae7f069e571a04808946b7aad6", "id": "werkzeug.wrappers.request", "ignore_all": true, "interface_hash": "35dc7aa2820bb077d96237b311e6b18db8eef470", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\wrappers\\request.py", "plugin_data": null, "size": 24620, "suppressed": [], "version_id": "1.15.0"}