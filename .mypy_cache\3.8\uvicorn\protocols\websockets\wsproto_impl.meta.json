{"data_mtime": 1753889309, "dep_lines": [28, 7, 10, 11, 12, 13, 15, 26, 27, 35, 1, 3, 4, 5, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["uvicorn.protocols.utils", "urllib.parse", "wsproto.events", "wsproto.connection", "wsproto.extensions", "wsproto.utilities", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "logging", "typing", "wsproto", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.events", "asyncio.locks", "asyncio.protocols", "asyncio.queues", "asyncio.transports", "enum", "types", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.websockets_impl"], "hash": "d1ccb415b1db7ed7af645ac60d50f382fd0e2b5b", "id": "uvicorn.protocols.websockets.wsproto_impl", "ignore_all": true, "interface_hash": "1a17ed3de1ea32a396867433d3d1a0d97cf68949", "mtime": 1750470764, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py", "plugin_data": null, "size": 15375, "suppressed": [], "version_id": "1.15.0"}