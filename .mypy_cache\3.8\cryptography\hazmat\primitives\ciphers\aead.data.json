{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives.ciphers.aead", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AESCCM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM", "name": "AESCCM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.aead", "mro": ["cryptography.hazmat.primitives.ciphers.aead.AESCCM", "builtins.object"], "names": {".class": "SymbolTable", "_MAX_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM._MAX_SIZE", "name": "_MAX_SIZE", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "tag_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "tag_length"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESCCM", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AESCCM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM._check_params", "name": "_check_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESCCM", "builtins.bytes", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_params of AESCCM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM._key", "name": "_key", "type": "builtins.bytes"}}, "_tag_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM._tag_length", "name": "_tag_length", "type": "builtins.int"}}, "_validate_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "nonce", "data_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM._validate_lengths", "name": "_validate_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "nonce", "data_len"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESCCM", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_lengths of AESCCM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decrypt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM.decrypt", "name": "decrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESCCM", "builtins.bytes", "builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decrypt of AESCCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encrypt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM.encrypt", "name": "encrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESCCM", "builtins.bytes", "builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encrypt of AESCCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "bit_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM.generate_key", "name": "generate_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "bit_length"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.aead.AESCCM"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_key of AESCCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM.generate_key", "name": "generate_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "bit_length"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.aead.AESCCM"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_key of AESCCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESCCM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.aead.AESCCM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AESGCM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM", "name": "AESGCM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.ciphers.aead", "mro": ["cryptography.hazmat.primitives.ciphers.aead.AESGCM", "builtins.object"], "names": {".class": "SymbolTable", "_MAX_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM._MAX_SIZE", "name": "_MAX_SIZE", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESGCM", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AESGCM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM._check_params", "name": "_check_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESGCM", "builtins.bytes", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_params of AESGCM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM._key", "name": "_key", "type": "builtins.bytes"}}, "decrypt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM.decrypt", "name": "decrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESGCM", "builtins.bytes", "builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decrypt of AESGCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encrypt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM.encrypt", "name": "encrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nonce", "data", "associated_data"], "arg_types": ["cryptography.hazmat.primitives.ciphers.aead.AESGCM", "builtins.bytes", "builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encrypt of AESGCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "bit_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM.generate_key", "name": "generate_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "bit_length"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.aead.AESGCM"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_key of AESGCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM.generate_key", "name": "generate_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "bit_length"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.aead.AESGCM"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_key of AESGCM", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.ciphers.aead.AESGCM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AESGCMSIV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESGCMSIV", "line": 26, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.aead.AESGCMSIV"}}, "AESOCB3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESOCB3", "line": 25, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.aead.AESOCB3"}}, "AESSIV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.ciphers.aead.AESSIV", "line": 24, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.aead.AESSIV"}}, "ChaCha20Poly1305": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.ciphers.aead.ChaCha20Poly1305", "line": 23, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.aead.ChaCha20Poly1305"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.ciphers.aead.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aead": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.backends.openssl.aead", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "backend": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.backends.openssl.backend.backend", "kind": "Gdef", "module_public": false}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "rust_openssl": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl", "kind": "Gdef", "module_public": false}, "utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.utils", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py"}