{"data_mtime": 1753889304, "dep_lines": [33, 34, 5, 7, 21, 30, 31, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.http", "werkzeug.sansio.utils", "urllib.parse", "werkzeug.datastructures", "werkzeug.http", "werkzeug.user_agent", "werkzeug.utils", "__future__", "typing", "datetime", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions", "werkzeug._internal", "werkzeug.datastructures.accept", "werkzeug.datastructures.auth", "werkzeug.datastructures.cache_control", "werkzeug.datastructures.etag", "werkzeug.datastructures.headers", "werkzeug.datastructures.mixins", "werkzeug.datastructures.range", "werkzeug.datastructures.structures"], "hash": "eccbd5f8aa241579cd5b71bebe119a9736b389ee", "id": "werkzeug.sansio.request", "ignore_all": true, "interface_hash": "cd2db9925b280b77bb65adbc7d9f78dc4f673287", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\sansio\\request.py", "plugin_data": null, "size": 19954, "suppressed": [], "version_id": "1.15.0"}