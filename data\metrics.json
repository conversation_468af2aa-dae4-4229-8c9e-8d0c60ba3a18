{"start_time": 1753491854.6677527, "saved_at": 1753895175.2489097, "request_count": {"POST /proxy/metrics/reset": 1, "GET /proxy/metrics": 476, "GET /proxy/status": 146, "POST /api/generate": 160, "POST /api/chat": 13, "GET /api/tags": 52, "GET /": 3, "GET /api/version": 3, "POST /proxy/metrics/cleanup": 1}, "request_duration": {"POST /proxy/metrics/reset": [0.008079051971435547], "GET /proxy/metrics": [0.008045434951782227, 0.007886886596679688, 0.0070476531982421875, 0.007752180099487305, 0.008368253707885742, 0.009185314178466797, 0.007602214813232422, 0.007992267608642578, 0.007399797439575195, 0.008478164672851562, 0.008679389953613281, 0.007705211639404297, 0.008083581924438477, 0.00758051872253418, 0.006877422332763672, 0.007160186767578125, 0.008658885955810547, 0.009223699569702148, 0.008420705795288086, 0.007955312728881836, 0.009169578552246094, 0.007774829864501953, 0.009002447128295898, 0.008591890335083008, 0.011780977249145508, 0.013661861419677734, 0.008112192153930664, 0.008782148361206055, 0.0072422027587890625, 0.011186361312866211, 0.009172677993774414, 0.0073397159576416016, 0.011472225189208984, 0.009166479110717773, 0.008888006210327148, 0.01035761833190918, 0.008949041366577148, 0.01141214370727539, 0.010050773620605469, 0.009212732315063477, 0.009618759155273438, 0.008445024490356445, 0.009115934371948242, 0.007598400115966797, 0.007760763168334961, 0.009383440017700195, 0.007117033004760742, 0.00888371467590332, 0.008780956268310547, 0.00911569595336914, 0.008228540420532227, 0.00799703598022461, 0.008250951766967773, 0.009504556655883789, 0.0075244903564453125, 0.007738351821899414, 0.009125947952270508, 0.008040666580200195, 0.007828235626220703, 0.008877754211425781, 0.008213281631469727, 0.00979304313659668, 0.0077114105224609375, 0.008168697357177734, 0.00816965103149414, 0.007351398468017578, 0.0072400569915771484, 0.0066683292388916016, 0.0067462921142578125, 0.006287813186645508, 0.007502555847167969, 0.008016586303710938, 0.007263898849487305, 0.006738901138305664, 0.006880044937133789, 0.0071599483489990234, 0.008043527603149414, 0.006896018981933594, 0.008035898208618164, 0.009510040283203125, 0.011815309524536133, 0.006998777389526367, 0.007908821105957031, 0.00822591781616211, 0.010822057723999023, 0.007231235504150391, 0.007803678512573242, 0.008255720138549805, 0.008270740509033203, 0.006936311721801758, 0.007305145263671875, 0.00834512710571289, 0.008047342300415039, 0.00630950927734375, 0.008247852325439453, 0.008212566375732422, 0.008005619049072266, 0.0077784061431884766, 0.007244110107421875, 0.0065042972564697266], "GET /proxy/status": [0.0008294582366943359, 0.0003123283386230469, 0.00030159950256347656, 0.0002052783966064453, 0.00022268295288085938, 0.0003654956817626953, 0.00028705596923828125, 0.0001838207244873047, 0.0011794567108154297, 0.0003266334533691406, 0.0003528594970703125, 0.00025272369384765625, 0.00025010108947753906, 0.00039386749267578125, 0.0003070831298828125, 0.0012230873107910156, 0.00016808509826660156, 0.00016832351684570312, 0.00014925003051757812, 0.00018286705017089844, 0.00017404556274414062, 0.000164031982421875, 0.0002474784851074219, 0.00016736984252929688, 0.00025534629821777344, 0.00019741058349609375, 0.0002090930938720703, 0.00016355514526367188, 0.0005173683166503906, 0.0001857280731201172, 0.0002033710479736328, 0.0008034706115722656, 0.0004088878631591797, 0.00028824806213378906, 0.00017762184143066406, 0.0001842975616455078, 0.00018858909606933594, 0.00017213821411132812, 0.00030732154846191406, 0.0002200603485107422, 0.0008270740509033203, 0.0003063678741455078, 0.0002048015594482422, 0.0002086162567138672, 0.0002429485321044922, 0.0001850128173828125, 0.0001800060272216797, 0.00021219253540039062, 0.00013780593872070312, 0.00014710426330566406, 0.0003409385681152344, 0.0004057884216308594, 0.0005624294281005859, 0.0010769367218017578, 0.004357337951660156, 0.0005192756652832031, 0.0004799365997314453, 0.0002617835998535156, 0.0002868175506591797, 0.0002300739288330078, 0.00020456314086914062, 0.0003211498260498047, 0.00037288665771484375, 0.0002574920654296875, 0.0002980232238769531, 0.0005414485931396484, 0.0003859996795654297, 0.0002925395965576172, 0.0001952648162841797, 0.00020766258239746094, 0.0004367828369140625, 0.0002529621124267578, 0.00017642974853515625, 0.00019073486328125, 0.00023245811462402344, 0.0005865097045898438, 0.0005099773406982422, 0.0003247261047363281, 0.00047135353088378906, 0.0002071857452392578, 0.00023102760314941406, 0.00018644332885742188, 0.0002155303955078125, 0.0002582073211669922, 0.00019788742065429688, 0.0001914501190185547, 0.0003867149353027344, 0.0002949237823486328, 0.00021719932556152344, 0.00019431114196777344, 0.0017826557159423828, 0.00017976760864257812, 0.0002837181091308594, 0.00017261505126953125, 0.00017905235290527344, 0.0001633167266845703, 0.0002751350402832031, 0.0003380775451660156, 0.0003294944763183594, 0.00018978118896484375], "POST /api/generate": [4.134925603866577, 0.0022373199462890625, 8.091779470443726, 0.9088726043701172, 13.775399208068848, 5.974713087081909, 2.395066499710083, 6.104565382003784, 0.8146324157714844, 32.67900538444519, 6.220295190811157, 24.00834035873413, 0.9904773235321045, 13.16027545928955, 3.0255918502807617, 3.8133952617645264, 14.467668056488037, 2.3554446697235107, 0.37918615341186523, 0.3618583679199219, 0.3296229839324951, 3.8296689987182617, 4.87405252456665, 2.1995954513549805, 8.629430294036865, 35.2440242767334, 10.740895986557007, 7.762277126312256, 4.787411212921143, 36.21790075302124, 0.000438690185546875, 13.757683515548706, 8.067044734954834, 12.760895729064941, 4.750929594039917, 23.960748195648193, 0.0002758502960205078, 10.550098180770874, 1.5877985954284668, 3.898238182067871, 1.1575684547424316, 0.3159515857696533, 0.3804447650909424, 0.7113022804260254, -0.04937386512756348, 0.43117189407348633, 7.1972105503082275, 21.889899969100952, 9.187590599060059, 4.177763223648071, 0.00033855438232421875, 0.0002758502960205078, 6.659513235092163, 0.10174036026000977, 0.0002307891845703125, 0.00020933151245117188, 3.896766424179077, 2.821174383163452, 5.953388690948486, 5.519206285476685, 4.56959867477417, 2.067125082015991, 20.651923179626465, 3.766080379486084, 0.5877985954284668, 0.616471529006958, 0.7179458141326904, 0.6144704818725586, 9.100614070892334, 27.21884775161743, 18.188387632369995, 301.31502866744995, 44.06521201133728, 3.814056396484375, 13.79568076133728, 2.670806407928467, 5.537399530410767, 9.772827625274658, 1.2902545928955078, 5.954418659210205, 14.544468402862549, 13.044536828994751, 23.921113967895508, 20.32419729232788, 9.94236969947815, 11.420101881027222, 62.68362379074097, 319.766321182251, 319.76805901527405, 0.0005795955657958984, 0.0007529258728027344, 0.0003750324249267578, 0.0008020401000976562, 3.3910105228424072, 0, 0, 12.411436080932617, 25.014324426651, 13.178094387054443, 15.15416431427002], "POST /api/chat": [5.147516489028931, 15.463821172714233, 5.294865846633911, -0.8523826599121094, 7.381824970245361, 48.02816462516785, 7.350108623504639, 4.9162328243255615, -0.3877279758453369, 2.2298800945281982, 19.398632764816284, 4.313049554824829, 4.870074033737183], "GET /api/tags": [0.08531856536865234, 0.09807777404785156, 0.10579085350036621, 0.13324666023254395, 0.07547712326049805, 0.0927591323852539, 0.10317230224609375, 0.07257795333862305, 0.06013846397399902, 0.05866193771362305, 0.06563925743103027, 0.06643319129943848, 0.07505011558532715, 0.07096624374389648, 0.12537813186645508, 0.08399629592895508, 0.07012248039245605, 0.07898163795471191, 0.10027384757995605, 0.10317397117614746, 0.09221887588500977, 0.10700464248657227, 0.24408698081970215, 0.10001111030578613, 0.07370495796203613, 0.7951929569244385, 0.07008981704711914, 0.05976510047912598, 0.0738368034362793, 0.08251380920410156, 0.06316924095153809, 1.0810925960540771, 0.08147621154785156, 0.07912230491638184, 0.07593846321105957, 0.07866549491882324, 0.07825732231140137, 0.07563233375549316, 0.07648801803588867, 0.06090569496154785, 0.07546138763427734, 0.07771086692810059, 0.07794356346130371, 0.07906079292297363, 0.04786825180053711, 0.06782817840576172, 0.07309484481811523, 0.06054186820983887, 0.1295483112335205, 0.08075594902038574, 0.06635880470275879, 0.08038902282714844], "GET /": [2.1457672119140625e-06, 1.9073486328125e-06, 2.6226043701171875e-06], "GET /api/version": [1.9073486328125e-06, 1.9073486328125e-06, 3.0994415283203125e-06], "POST /proxy/metrics/cleanup": [0.007741451263427734]}, "error_count": {"POST /api/generate": 15}, "status_codes": {"200": 48, "500": 1}, "host_request_count": {"http://************:11434": 38, "http://************:11434": 28, "http://************:11434": 20, "http://************:11434": 52}, "host_error_count": {"http://************:11434": 3}, "host_response_times": {"http://************:11434": [0.4419581890106201, 8.546696186065674, 15.417922258377075, 12.164761304855347, 5.147516489028931, 15.463821172714233, 1.1653144359588623, 7.260696172714233, 0.6072688102722168, 1.2703227996826172, 25.129063844680786, 40.444517612457275, 1.5070714950561523, 0.4231138229370117, 4.638781547546387, 0.3221919536590576, 4.879332780838013, 0.8547573089599609, 6.621678590774536, 0.5162334442138672, 25.15186882019043, 8.465997219085693, 1.114194393157959, 24.00834035873413, 0.9904773235321045, 14.467668056488037, 2.1995954513549805, 0.3159515857696533, 0.43117189407348633, 9.187590599060059, 4.177763223648071, 6.659513235092163, 0.10174036026000977, 27.21884775161743, 18.188387632369995, 301.31502866744995, 44.06521201133728, 5.537399530410767], "http://************:11434": [-0.8523826599121094, 7.381824970245361, -0.3877279758453369, 4.040196657180786, 58.52020192146301, 89.66045188903809, 4.975370407104492, 2.35306978225708, 2.5122764110565186, 48.20158934593201, 2.2298800945281982, 3.8133952617645264, 2.3554446697235107, 0.37918615341186523, 0.3618583679199219, 0.3296229839324951, 3.8296689987182617, 8.629430294036865, 10.740895986557007, 7.762277126312256, 4.787411212921143, 8.067044734954834, 12.760895729064941, 4.750929594039917, 1.5877985954284668, 0.3804447650909424, 9.100614070892334, 3.814056396484375], "http://************:11434": [48.02816462516785, 56.0153546333313, 52.720155477523804, 18.4600567817688, 15.490460634231567, 14.013548374176025, 19.398632764816284, 13.16027545928955, 3.0255918502807617, 35.2440242767334, 36.21790075302124, 13.757683515548706, 23.960748195648193, 10.550098180770874, 3.898238182067871, 0.7113022804260254, 7.1972105503082275, 21.889899969100952, 20.651923179626465, 13.79568076133728], "http://************:11434": [11.191508293151855, 3.579657554626465, 4.0648157596588135, 1.4524481296539307, 0.8863584995269775, 0.5724189281463623, 0.5644886493682861, 5.51922345161438, 4.4136927127838135, 3.40250301361084, 4.134925603866577, 8.091779470443726, 0.9088726043701172, 13.775399208068848, 5.974713087081909, 2.395066499710083, 6.104565382003784, 32.67900538444519, 6.220295190811157, 4.87405252456665, 1.1575684547424316, -0.04937386512756348, 3.896766424179077, 2.821174383163452, 5.953388690948486, 5.519206285476685, 4.56959867477417, 2.067125082015991, 3.766080379486084, 0.5877985954284668, 0.616471529006958, 0.7179458141326904, 0.6144704818725586, 2.670806407928467, 9.772827625274658, 1.2902545928955078, 5.954418659210205, 14.544468402862549, 13.044536828994751, 23.921113967895508, 20.32419729232788, 9.94236969947815, 11.420101881027222, 62.68362379074097, 319.766321182251, 3.3910105228424072, 0, 0, 12.411436080932617, 25.014324426651, 13.178094387054443, 15.15416431427002]}, "model_request_count": {"llama3.2:3b": 14, "granite3.3:8b": 6, "llama3:latest": 66, "tinyllama:latest": 17, "dolphin-mistral:7b": 2, "deepseek-r1:1.5b": 8, "phi:latest": 23, "smollm2:1.7b": 2, "smollm:135m": 2, "smollm2:latest": 3, "smollm:360m": 2, "llama3.1:latest": 1, "gemma3:latest": 7, "codellama:7b": 1, "codellama:7b-instruct": 1, "Deepcoder:latest": 1, "deepseek-coder:6.7b": 1, "deepseek-r1:8b": 16}, "model_error_count": {"llama3.2:3b": 1, "phi:latest": 2, "smollm2:latest": 2, "smollm:360m": 2, "deepseek-r1:8b": 8}, "model_host_usage": {"llama3.2:3b": {"http://************:11434": 13}, "granite3.3:8b": {"http://************:11434": 6}, "llama3:latest": {"http://************:11434": 11, "http://************:11434": 7, "http://************:11434": 4, "http://************:11434": 24}, "tinyllama:latest": {"http://************:11434": 4, "http://************:11434": 5, "http://************:11434": 4, "http://************:11434": 1}, "dolphin-mistral:7b": {"http://************:11434": 2}, "deepseek-r1:1.5b": {"http://************:11434": 5, "http://************:11434": 3}, "phi:latest": {"http://************:11434": 10, "http://************:11434": 4, "http://************:11434": 4, "http://************:11434": 3}, "smollm2:1.7b": {"http://************:11434": 2}, "smollm:135m": {"http://************:11434": 2}, "smollm2:latest": {"http://************:11434": 1}, "llama3.1:latest": {"http://************:11434": 1}, "gemma3:latest": {"http://************:11434": 7}, "codellama:7b": {"http://************:11434": 1}, "codellama:7b-instruct": {"http://************:11434": 1}, "Deepcoder:latest": {"http://************:11434": 1}, "deepseek-coder:6.7b": {"http://************:11434": 1}, "deepseek-r1:8b": {"http://************:11434": 11}}, "recent_requests": [{"timestamp": "2025-07-30 09:46:52.846527", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:47:20.460566", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:47:48.116754", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:48:15.717322", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:48:43.332867", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:49:11.041563", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:49:38.669157", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:50:06.270384", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:50:33.854686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:51:01.514943", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:51:29.166193", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:51:56.780282", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:52:24.437997", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:52:52.037695", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.53131103515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:53:19.571413", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:53:47.259147", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:54:14.844281", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:54:42.398174", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:55:09.967778", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:55:37.618550", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.866455078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:56:05.247640", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:56:32.896195", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:57:00.439581", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:57:28.057638", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:57:55.730477", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:58:23.278677", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:58:50.950263", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:59:18.528804", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:59:46.121289", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00017642974853515625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:00:13.761145", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.937980651855469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:00:41.356411", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:01:08.932141", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:01:36.534083", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.914138793945312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:02:04.125132", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:02:31.761689", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:02:59.322158", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:03:26.944672", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:03:54.484248", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:04:22.057743", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:04:49.627170", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:05:17.223496", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.0558319091796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:05:44.750659", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:06:12.317820", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:06:39.913663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:07:07.536057", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.984306335449219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:07:35.183291", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:08:02.765135", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.152557373046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:08:30.315887", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:08:57.916956", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:09:25.481845", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:09:53.058347", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:10:20.650528", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:10:48.236176", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:11:15.832255", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:11:43.431608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:12:10.976619", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:12:38.534008", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.888938903808594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:13:06.168041", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:13:33.682735", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:14:01.255638", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:14:28.837449", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:14:56.362977", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:15:23.881412", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00013828277587890625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:15:51.556146", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.887580871582031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:16:19.081524", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:16:46.631047", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014209747314453125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:17:14.219270", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.605552673339844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:17:41.753853", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:18:09.265463", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:18:36.779164", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:19:04.387004", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:19:31.928999", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:19:59.484619", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:20:27.019446", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:20:54.562278", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:21:22.084568", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:21:49.606523", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:22:17.138039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:22:44.637083", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:23:12.171942", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:23:39.744715", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.771087646484375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:24:07.266098", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:24:34.762686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:25:02.331048", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.817413330078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:25:29.872588", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:25:57.376118", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00010633468627929688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:26:24.862676", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:26:52.447040", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:27:19.997878", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:27:47.492853", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:28:15.023449", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:28:42.533443", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014138221740722656, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:29:09.997568", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.510185241699219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:29:37.568433", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.553794860839844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:30:05.058990", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:30:32.378205", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.841255187988281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:31:00.077267", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.340576171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:31:27.594208", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:31:55.078321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.1484832763671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:32:22.600256", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:32:50.116510", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:33:17.633418", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:33:45.158564", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:34:12.651578", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:34:40.163587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.506111145019531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:35:07.659239", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:35:35.178245", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:36:02.685498", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.650520324707031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:36:30.178630", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:36:57.645343", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:37:25.208837", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:37:52.695612", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:38:20.227796", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:38:47.741604", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:39:15.241618", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.82012939453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:39:42.706556", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:40:10.216588", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:40:37.712100", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:41:05.218264", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:41:32.696338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.678436279296875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:42:00.196562", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:42:27.700821", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:42:55.173321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:43:22.742744", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:43:50.195414", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:44:17.690822", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:44:45.183748", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:45:11.749526", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011610984802246094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:45:39.227018", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:46:05.055849", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:46:32.540472", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.130073547363281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:47:00.026927", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:47:27.497017", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.887580871582031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:47:55.002178", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:48:22.498104", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:48:49.991625", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:49:17.524923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:49:45.035098", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.699562072753906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:50:12.582790", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:50:39.973585", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.601478576660156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:51:07.601535", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.8650970458984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:51:35.109274", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:52:02.591106", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:52:30.082317", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:52:57.582492", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:53:25.113285", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:53:52.575923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:54:20.070595", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.390975952148438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:54:47.553711", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:55:15.025497", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:55:42.530495", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:56:10.018086", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.322166442871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:56:37.443334", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:57:04.904401", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:57:32.373662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:57:59.835793", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:58:27.320873", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:58:54.801205", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:59:22.292714", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.601478576660156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:59:49.960534", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.981590270996094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:00:17.695483", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.982948303222656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:00:45.154616", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:01:12.661392", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:01:40.127844", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.935264587402344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:02:07.666958", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:02:35.154924", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:03:02.636689", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.794929504394531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:03:30.110916", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:03:57.616381", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:04:25.218564", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:04:52.784735", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.030632019042969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:05:20.303645", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:05:47.772314", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:06:15.222187", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:06:42.724772", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:07:10.169644", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.100799560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:07:37.687214", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:08:05.195692", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:08:32.662260", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:09:00.096665", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:09:27.630879", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:09:55.093230", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:10:22.610917", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:10:50.097317", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:11:17.592185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:11:45.069160", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:12:12.543244", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:12:39.979761", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:13:07.553936", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:13:35.025652", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:14:02.545923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:14:30.037649", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:14:57.437738", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:15:24.893107", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:15:52.352012", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:16:19.953949", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:16:47.448647", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:17:14.918424", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:17:42.364380", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:18:09.851631", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:18:37.380390", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:19:04.834723", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:19:32.316717", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:19:59.723688", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:20:27.288845", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.459785461425781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:20:54.717291", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:21:22.176330", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:21:49.582404", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:22:17.033507", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.363059997558594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:22:44.504990", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:23:12.044466", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:23:39.443366", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:24:06.878370", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:24:34.334384", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:25:01.844593", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:25:29.301409", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:25:56.754532", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:26:24.318789", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:26:51.734013", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.793571472167969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:27:19.212877", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:27:46.704969", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:28:14.168587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:28:41.611316", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:29:09.075057", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:29:36.548338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:30:04.044716", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.863739013671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:30:31.432592", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:30:58.858196", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:31:26.342905", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:31:53.746736", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:32:21.255521", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:32:48.752216", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:33:16.244090", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:33:43.671467", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:34:11.145096", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:34:38.583823", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:35:06.081562", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:35:33.510335", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:36:00.954860", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:36:28.412829", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:36:55.859805", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.482269287109375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:37:23.280051", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:37:50.763350", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:38:18.227759", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:38:45.762589", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:39:13.162424", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:39:40.554173", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.054473876953125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:40:08.009685", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:40:35.472491", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:41:02.913363", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:41:30.406519", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:41:57.920527", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:42:25.416927", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:42:52.887869", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:43:20.329372", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:43:47.787244", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:44:15.228577", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:44:42.587811", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:45:09.976791", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:45:37.380474", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:46:04.846201", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:46:32.246977", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.459785461425781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:46:59.758387", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:47:27.135046", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:47:54.555608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:48:21.952398", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.0531158447265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:48:49.381566", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:49:16.816835", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:49:44.240813", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.915496826171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:50:11.703495", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:04.552013", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:05.562929", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009335517883300781, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:05.576592", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005624294281005859, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:05.585535", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008174896240234375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:31.962898", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.367134094238281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:59.607720", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.0558319091796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:02:27.049808", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:02:54.461150", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.341934204101562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:03:21.879062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:03:49.313997", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.6743621826171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:04:16.889460", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.698204040527344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:04:44.330719", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.58306884765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:05:11.765722", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:05:39.173431", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:06:06.656124", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.246566772460938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:06:34.075353", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.8650970458984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:07:01.729211", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.5789947509765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:07:29.176512", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.482269287109375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:07:56.690608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:08:24.142021", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00012230873107910156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:08:51.608967", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.482269287109375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:09:19.004105", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:09:46.416286", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.984306335449219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:10:13.869505", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.821487426757812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:10:41.262160", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:11:08.647433", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:11:36.085037", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:12:03.634964", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:12:31.131385", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.8160552978515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:12:58.572938", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.650520324707031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:13:26.029444", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:13:53.457559", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011920928955078125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:14:20.644624", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:14:48.252682", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.029273986816406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:15:15.644729", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:15:43.046706", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.316734313964844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:16:10.499673", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:16:37.951938", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00015091896057128906, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:17:05.345069", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.796287536621094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:17:32.771993", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014495849609375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:18:00.209437", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:18:27.646671", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.0001437664031982422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:18:55.048654", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:19:22.459508", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:19:49.858587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.914138793945312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:20:17.393141", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.673004150390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:20:44.781793", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.270408630371094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:21:12.189246", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.364418029785156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:21:39.584428", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:22:07.008662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.535385131835938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:22:34.436413", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00019693374633789062, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:23:01.977247", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:23:29.467445", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.940696716308594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:23:56.816806", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.985664367675781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:16.276525", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013486146926879883, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:16.297401", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008265018463134766, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:16.302221", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0010769367218017578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:18.238800", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009555816650390625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:21.337867", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10027384757995605, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:24.231015", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.584426879882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:51.666974", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.910064697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:25:19.065241", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:25:46.498225", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:25:59.419788", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 62.68362379074097, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": null, "prompt_eval_count": 26, "eval_count": 4220, "prompt_eval_duration": 199145300, "eval_duration": 56777758000, "total_duration": 64316657300}, {"timestamp": "2025-07-30 12:26:13.888372", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.630752563476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:26:41.328937", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.557868957519531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:27:08.710127", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00013113021850585938, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:27:36.122045", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00013494491577148438, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:28:03.565160", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.0001010894775390625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:28:31.050151", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.870529174804688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:29:08.008941", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:29:35.421050", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.869171142578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:03.104284", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011992454528808594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:30.522968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.510185241699219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:40.118239", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.014506340026855469, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:40.143114", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007960081100463867, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:40.150915", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.004357337951660156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:57.918404", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011992454528808594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:31:25.340426", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00016808509826660156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:31:52.765821", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.008148193359375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:32:20.222768", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.38690185546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:32:48.143968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.863739013671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:33:15.537556", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.632110595703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:33:42.927689", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:12.152835", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:37.796396", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:55.866534", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008409738540649414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:55.893291", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013602018356323242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:55.897115", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005192756652832031, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:58.775826", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.014334440231323242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:35:00.334977", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10317397117614746, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:35:05.202171", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:35:32.594151", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.580352783203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:36:00.037631", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.747245788574219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:36:27.432161", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:36:54.854476", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.267692565917969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:37:22.254852", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:37:49.725257", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:38:17.095698", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.269050598144531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:38:44.536463", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.743171691894531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:39:11.899538", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:39:39.299981", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:40:06.686793", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:40:31.286925", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 319.766321182251, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": "RemoteProtocolError"}, {"timestamp": "2025-07-30 12:40:31.287707", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 319.76805901527405, "host": null, "model": "deepseek-r1:8b", "error": "RemoteProtocolError"}, {"timestamp": "2025-07-30 12:40:34.068641", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.702278137207031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:01.455758", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.654594421386719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:06.008446", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0005795955657958984, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:41:23.875745", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0007529258728027344, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:41:28.872400", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.128715515136719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:34.026577", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.019033193588256836, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:41.700085", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.09221887588500977, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:54.414397", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0003750324249267578, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:41:56.283087", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.250640869140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:03.443554", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011011838912963867, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:03.473743", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.012443304061889648, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:03.482282", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0004799365997314453, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:05.387421", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010657548904418945, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:05.398697", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002617835998535156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:05.407873", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008765220642089844, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:08.409165", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00939321517944336, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:10.021808", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10700464248657227, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:22.206922", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0008020401000976562, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:42:23.675930", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.58306884765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:51.088380", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00016736984252929688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:57.651826", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013236284255981445, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:06.080957", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.24408698081970215, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:14.527368", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.01015782356262207, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:18.497627", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.220008850097656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:18.959471", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10001111030578613, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:35.699629", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 3.3910105228424072, "host": "http://************:11434", "model": "tinyllama:latest", "error": null, "prompt_eval_count": 35, "eval_count": 376, "prompt_eval_duration": 169818800, "eval_duration": **********, "total_duration": **********}, {"timestamp": "2025-07-30 12:43:45.941243", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:44:13.336625", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.273124694824219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:44:40.755690", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:45:08.153224", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.198883056640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:45:35.542926", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:46:02.956062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:46:30.362281", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:46:57.766681", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00010180473327636719, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:47:25.161991", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.723403930664062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:47:52.607314", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.866455078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:48:20.071486", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.987022399902344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:48:47.461433", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:49:14.844530", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:49:42.268625", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.771087646484375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:50:09.694417", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.793571472167969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:50:37.075323", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.553794860839844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:51:04.469038", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.508827209472656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:51:31.878040", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:51:59.253167", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:52:26.678410", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.270408630371094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:52:54.060444", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:53:21.472534", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:53:48.891721", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.365776062011719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:54:16.313206", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:54:43.695698", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:55:11.069703", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.029273986816406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:55:38.447557", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:56:05.877497", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:56:33.294262", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:39.622006", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:40.843229", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008229255676269531, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:40.865770", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008163928985595703, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:40.869754", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002868175506591797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:00:06.933134", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.00543212890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:00:34.353917", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:01:01.769265", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.124641418457031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:01:29.143484", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:01:56.497406", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:02:23.870720", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.249270", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009141683578491211, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.270983", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008946418762207031, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.273812", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002300739288330078, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.716235", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:38.085007", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:44.037185", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009300470352172852, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:44.055357", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007836103439331055, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:44.058668", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00020456314086914062, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:45.713825", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008150339126586914, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:46.883063", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07370495796203613, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:06:05.469321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:06:32.860211", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:07:00.241384", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:07:27.628923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:07:55.005861", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:08:22.370132", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:08:49.755820", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:09:17.126678", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:09:44.622610", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:09:45.624387", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.7951929569244385, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.614201", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010433673858642578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.642006", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009443521499633789, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.645121", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003211498260498047, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.677685", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00010538101196289062, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:47.439462", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008045434951782227, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:47.459031", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007886886596679688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:47.462151", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00037288665771484375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:48.058926", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:48.487018", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0070476531982421875, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:49.894121", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07008981704711914, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:14:15.367263", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.935264587402344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:14:17.028490", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.05976510047912598, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:14:42.948753", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.2411346435546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:15:10.333989", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:15:37.720608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:16:05.103698", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.53131103515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:16:32.468398", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:16:59.829691", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:17:27.211288", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:17:54.700880", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:18:22.159686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:18:49.531311", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:19:16.991356", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:19:44.377345", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.3603439331054688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:20:11.757641", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:20:39.101746", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:21:06.472072", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6702880859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:21:33.839084", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:22:01.220415", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:22:28.586338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:51.388416", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:52.583860", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007752180099487305, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:52.604776", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008368253707885742, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:52.608156", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002574920654296875, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:25:18.734816", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:25:46.269875", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:26:13.648907", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:26:41.026660", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:27:08.396738", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:27:35.849800", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:28:03.231639", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.291534423828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:28:30.699256", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.842613220214844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:28:58.096662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:29:25.473623", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:29:52.846115", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:30:20.236624", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:30:47.607982", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.127357482910156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:31:14.978014", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:31:42.340455", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:32:09.716839", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:32:37.122256", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:33:04.487257", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:33:31.856899", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:33:59.228116", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:34:26.629321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:34:53.991663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.866455078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:35:21.432840", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:35:48.800599", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:36:16.173749", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:36:43.582724", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:37:10.973196", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:37:38.326435", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:38:05.697221", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:38:33.074667", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.364418029785156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:39:00.429039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:39:27.811090", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:39:55.176364", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:40:22.540110", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:40:50.032284", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.125999450683594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:41:17.406834", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.291534423828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:41:44.808587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:42:12.171142", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.1484832763671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:42:39.535564", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:43:06.909312", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:43:34.280976", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:44:01.667781", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:44:29.024968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:44:56.382297", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.365776062011719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:45:23.753948", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00015044212341308594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:45:51.112330", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.341934204101562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:46:18.519244", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:46:45.893676", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.0001552104949951172, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:47:13.284954", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:47:40.696605", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:48:08.071787", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:48:35.461057", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:49:02.835004", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:49:30.192235", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:49:57.579228", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:24.938203", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.176399230957031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:50.225526", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.870529174804688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:51.482669", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009185314178466797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:51.504188", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007602214813232422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:51.508280", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002980232238769531, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:17.624041", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:39.730762", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007992267608642578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:39.748628", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007399797439575195, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:39.752101", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005414485931396484, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:45.002102", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:52:12.392368", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:53.290887", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.291534423828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:54.442283", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008478164672851562, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:54.465170", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008679389953613281, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:54.469184", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003859996795654297, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:10.029537", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007705211639404297, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:10.050857", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008083581924438477, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:10.054170", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002925395965576172, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:20.658304", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.030632019042969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:48.165185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:55:15.539839", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.2928924560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:55:42.944877", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:10.386900", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:37.758120", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:43.425179", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00758051872253418, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:43.441906", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006877422332763672, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:43.445562", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0001952648162841797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:48.205476", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007160186767578125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:48.225240", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008658885955810547, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:48.228348", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00020766258239746094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:05.114711", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.340576171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:30.469603", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:31.171833", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009223699569702148, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:31.194134", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008420705795288086, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:31.197970", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0004367828369140625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:57.835055", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.076957702636719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:58:25.262056", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.363059997558594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:58:52.652146", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:59:20.007645", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.935264587402344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:59:47.411002", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:02.120344", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007955312728881836, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:02.169052", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009169578552246094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:02.172290", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002529621124267578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:09.121953", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007774829864501953, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:09.197711", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009002447128295898, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:09.201458", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00017642974853515625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:14.767708", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:25.074261", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008591890335083008, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:26.463918", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.0738368034362793, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:42.121582", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:01:09.471052", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:47.067412", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:48.649030", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011780977249145508, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:48.674488", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013661861419677734, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:48.677879", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00019073486328125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:59.725492", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008112192153930664, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:59.744555", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008782148361206055, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:59.747707", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00023245811462402344, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:10:01.694298", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0072422027587890625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:10:03.413721", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.08251380920410156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:10:14.444725", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:55.688319", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:56.275998", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011186361312866211, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:56.300546", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009172677993774414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:56.304598", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005865097045898438, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:22.244973", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0073397159576416016, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:23.104435", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.459785461425781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:23.836449", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.06316924095153809, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:50.720558", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.00543212890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:14:17.900161", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:14:45.270949", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.202593", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.91278076171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.549599", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011472225189208984, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.575778", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005099773406982422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.585764", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009166479110717773, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:30.565303", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.2438507080078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:57.933762", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:18:25.389354", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:18:52.781773", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:19:20.145723", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:19:47.505214", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.886222839355469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:20:14.909012", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:20:42.305300", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:21:09.681138", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.679794311523438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:21:37.045306", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:22:04.414560", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:22:31.773305", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.43865966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:22:59.167039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:23:26.561289", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:23:53.934907", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:24:14.944359", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008888006210327148, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.362491", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.777526", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.01035761833190918, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.801496", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008949041366577148, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.806062", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003247261047363281, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:58.607782", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.01141214370727539, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:01.725901", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 1.0810925960540771, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:02.807271", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:13.274556", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": "UnboundLocalError"}, {"timestamp": "2025-07-30 14:27:30.140354", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:48.181362", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010050773620605469, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.223380", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.724761962890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.285361", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009212732315063477, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.300151", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00047135353088378906, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.310545", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009618759155273438, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:07.650844", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008445024490356445, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:09.175845", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.08147621154785156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:16.582216", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:36.031732", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07912230491638184, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:43.966684", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:31:11.335034", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.532798", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.2928924560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.871445", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009115934371948242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.892812", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007598400115966797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.895900", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002071857452392578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:38.971564", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007760763168334961, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:40.836342", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07593846321105957, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:47.895814", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.933906555175781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:33:15.262797", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:33:42.631316", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.076957702636719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:34:09.996327", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.291246", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009383440017700195, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.307808", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00023102760314941406, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.315431", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007117033004760742, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.423644", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.220008850097656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:51.798632", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.649162292480469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:56.796870", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00888371467590332, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:56.819987", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008780956268310547, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:56.822805", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00018644332885742188, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:58.143615", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00911569595336914, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:59.998496", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07866549491882324, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:19.277796", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:23.466580", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07825732231140137, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:28.717436", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008228540420532227, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:28.736375", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00799703598022461, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:28.739561", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002155303955078125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:30.681118", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008250951766967773, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:32.500661", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07563233375549316, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:46.665962", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:37:14.096302", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:37:41.460210", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:54:07.989098", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.937980651855469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:54:35.351834", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.435943603515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:55:02.727062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:55:30.100170", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:55:57.464942", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:12.848416", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009504556655883789, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:12.867347", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0075244903564453125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:12.870926", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002582073211669922, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:15.240501", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007738351821899414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:17.371740", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07648801803588867, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:24.848361", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:56:52.219696", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.029273986816406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:57:19.630890", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:57:47.042136", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9114227294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:58:14.397864", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00016999244689941406, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:58:41.767121", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:59:09.144302", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:59:28.022858", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009125947952270508, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:59:28.041484", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008040666580200195, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:59:28.044997", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00019788742065429688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:05:33.544010", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:06:00.924505", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.413459777832031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:06:28.310623", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:06:55.688360", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:07:22.440051", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:07:47.695597", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.743171691894531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:08:15.051002", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:08:42.403511", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:04.208809", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:09.770117", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014662742614746094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:37.157949", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.887580871582031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:40.045782", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.06090569496154785, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:42.169859", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007828235626220703, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:42.191103", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008877754211425781, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:42.194118", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0001914501190185547, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:45.906920", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008213281631469727, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:09:47.727425", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07546138763427734, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:10:06.344794", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": "AttributeError"}, {"timestamp": "2025-07-30 15:10:04.539649", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:10:31.917458", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.030632019042969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:15:42.289761", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00979304313659668, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:15:42.310891", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0077114105224609375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:15:42.314714", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003867149353027344, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:15:44.329623", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008168697357177734, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:15:44.501964", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.0558319091796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:15:45.857569", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07771086692810059, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:16:13.834025", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:16:43.169205", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:17:12.394272", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.743171691894531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:17:41.828549", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.1975250244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:21:37.055897", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.364418029785156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:22:06.781058", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:22:36.512319", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.435943603515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:23:06.351776", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:23:36.153496", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.507469177246094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:24:05.956930", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.839897155761719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:24:35.829390", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.984306335449219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:25:05.709554", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:25:35.574990", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:26:05.544185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:28:35.476708", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:05.498614", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.604194641113281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:28.911243", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00816965103149414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:28.933813", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007351398468017578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:28.936819", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002949237823486328, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:35.515079", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:37.934291", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0072400569915771484, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:29:40.701974", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07794356346130371, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:30:05.562996", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.910064697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:30:35.602974", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:05.676626", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.792213439941406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:35.723551", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.556510925292969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:36.681408", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0066683292388916016, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:36.701430", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0067462921142578125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:36.704539", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00021719932556152344, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:38.457679", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006287813186645508, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:31:39.615128", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07906079292297363, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:32:05.773593", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:32:35.832344", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:33:05.894813", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:36:06.246224", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:36:36.295090", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.743171691894531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:06.339021", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:35.358806", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007502555847167969, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:35.378618", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008016586303710938, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:35.381840", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00019431114196777344, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:36.415789", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:45.929928", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007263898849487305, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:37:47.745926", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.04786825180053711, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:38:06.470347", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.744529724121094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:40:36.902341", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:06.989856", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:15.495764", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006738901138305664, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:15.515658", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006880044937133789, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:15.520316", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0017826557159423828, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:17.151263", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0071599483489990234, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:18.379438", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.06782817840576172, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:41:37.048164", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:42:07.103642", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:42:37.161608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:43:07.241397", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.486343383789062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:43:37.307469", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:44:07.362731", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:44:37.412810", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:06.515150", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008043527603149414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:06.537562", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006896018981933594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:06.540352", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00017976760864257812, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:07.485461", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:08.057459", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008035898208618164, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:09.781657", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07309484481811523, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:45:37.549792", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:46:07.597633", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:46:37.674662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:47:07.759503", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:47:37.819078", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:48:07.904263", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7418136596679688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:48:37.971993", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:08.018470", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:26.709558", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009510040283203125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:26.737058", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011815309524536133, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:26.768364", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002837181091308594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:28.352115", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006998777389526367, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:29.512277", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.06054186820983887, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:49:38.075071", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:50:08.159569", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:50:38.221427", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:51:08.268864", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:51:38.325186", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:52:08.402381", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6464462280273438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:52:38.462784", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:08.543621", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:31.740507", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007908821105957031, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:31.764229", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00822591781616211, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:31.767047", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00017261505126953125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:33.380120", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010822057723999023, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:34.660558", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.1295483112335205, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:53:38.618741", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:54:08.675996", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:54:38.733891", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:08.797986", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:38.879962", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:51.742886", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007231235504150391, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:51.766214", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007803678512573242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:51.769312", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00017905235290527344, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:53.270842", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008255720138549805, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:55:54.499002", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.08075594902038574, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:56:08.942776", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.5510787963867188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:56:38.990604", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:57:09.060217", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:57:39.135242", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:58:09.228853", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.34600830078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:58:39.309230", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:59:09.398013", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 15:59:39.530903", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:00:09.585840", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:00:39.634188", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6702880859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:01:09.722120", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:01:39.770913", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:02:09.816919", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:02:39.855616", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:03:09.954419", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:03:40.050097", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:04:10.122101", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.5033950805664062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:04:40.190279", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:05:10.242014", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.696846008300781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:05:40.292550", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:06:10.333411", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:06:40.399575", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:07:10.451758", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.103515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:07:40.506885", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:08:10.580994", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:08:40.648831", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:09:10.693715", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:09:40.752094", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:10:10.796039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:10:40.847486", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:11:10.902107", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:11:40.953721", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:12:11.023426", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:12:41.091971", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:13:11.186321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:13:41.230631", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:14:11.283887", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:14:41.329669", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.5272369384765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:15:11.394067", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:15:41.445082", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.4836273193359375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:16:11.518411", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:16:41.589704", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:17:11.634582", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:17:41.670333", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:18:11.765065", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:18:41.837812", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:19:11.903580", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:19:41.948657", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:20:12.010393", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:20:42.069105", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:21:12.130747", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:21:42.171805", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.4345855712890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:22:12.221152", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:22:42.275614", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:23:12.330405", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:23:42.369198", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:24:12.411391", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:24:42.445497", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:25:12.474657", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:25:42.518769", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:26:12.563675", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:26:42.604365", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:27:12.641507", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6464462280273438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:27:42.622351", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:28:12.690589", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:28:42.738563", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:29:12.775662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:29:42.815332", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:30:12.851411", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:30:42.886653", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:31:12.930017", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:31:42.977359", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:07.504172", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008270740509033203, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:07.522122", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006936311721801758, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:07.552192", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0001633167266845703, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:09.144805", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007305145263671875, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:10.223544", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.06635880470275879, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:13.040415", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.910064697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:32:43.093888", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:33:13.135381", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:33:43.197168", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:34:13.233873", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:34:43.264065", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.1961669921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:35:13.322294", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.2438507080078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:35:43.388017", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:36:13.422949", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:36:43.464294", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:13.502262", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:18.240742", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00834512710571289, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:18.262736", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008047342300415039, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:18.267622", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002751350402832031, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:19.719654", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00630950927734375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:20.962521", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.08038902282714844, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:37:43.545176", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7418136596679688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:38:13.607852", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:38:43.659097", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:39:13.705231", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:39:43.750190", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:40:12.464175", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 12.411436080932617, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": null, "prompt_eval_count": 22, "eval_count": 982, "prompt_eval_duration": 10691400, "eval_duration": 12329457900, "total_duration": 12397665000}, {"timestamp": "2025-07-30 16:40:13.792702", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:40:43.829663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.5789947509765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:41:13.869378", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:41:43.903501", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:42:13.935813", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6464462280273438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:42:44.064156", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:43:14.097209", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:43:44.136409", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:44:14.165328", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:44:44.195290", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:45:02.325734", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008247852325439453, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:45:02.350929", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008212566375732422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:45:02.354692", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003380775451660156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:45:14.223279", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:45:44.233901", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:45:57.735481", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 25.014324426651, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": null, "prompt_eval_count": 22, "eval_count": 1717, "prompt_eval_duration": 189072700, "eval_duration": 21794509100, "total_duration": 24967230900}, {"timestamp": "2025-07-30 16:46:14.304841", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:46:44.333621", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:47:14.352851", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:47:44.364459", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:48:14.393146", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:48:35.887718", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008005619049072266, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:48:35.908876", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0077784061431884766, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:48:35.912730", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003294944763183594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:48:44.426634", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:49:14.484181", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:49:23.486202", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 13.178094387054443, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": null, "prompt_eval_count": 22, "eval_count": 1047, "prompt_eval_duration": 11825800, "eval_duration": 13010857100, "total_duration": 13083665300}, {"timestamp": "2025-07-30 16:49:44.514547", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:50:14.528529", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:50:44.556890", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:50:55.465598", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007244110107421875, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:50:55.484842", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0065042972564697266, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:50:55.487960", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00018978118896484375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:51:14.566592", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:51:25.156782", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 15.15416431427002, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": null, "prompt_eval_count": 22, "eval_count": 1206, "prompt_eval_duration": 11667600, "eval_duration": 15035664100, "total_duration": 15104022200}, {"timestamp": "2025-07-30 16:51:44.599270", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.4345855712890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:52:14.618607", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:52:44.644284", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:53:14.667282", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:53:44.684105", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:54:14.706582", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:54:44.724004", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:55:14.742800", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.410743713378906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:55:44.766271", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:56:14.783079", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:56:44.818138", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:57:14.851362", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:57:44.867253", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:58:14.880629", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:58:44.890693", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:59:14.912876", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.100799560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 16:59:44.921009", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.601478576660156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:00:14.947242", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:00:44.973087", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:01:15.004917", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:01:45.051061", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:02:15.080888", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:02:45.114010", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:03:15.140937", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:03:45.146659", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:04:15.169158", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7418136596679688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:04:45.192436", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:05:15.204438", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:05:45.224966", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 17:06:15.248552", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}], "health_check_count": 9172, "health_check_errors": 109, "health_checks": {"http://************:11434": [{"timestamp": "2025-07-30 16:41:30.256223", "healthy": true, "response_time": 0.014348030090332031}, {"timestamp": "2025-07-30 16:42:00.258093", "healthy": true, "response_time": 0.013725757598876953}, {"timestamp": "2025-07-30 16:42:30.346648", "healthy": true, "response_time": 0.01377558708190918}, {"timestamp": "2025-07-30 16:43:00.367180", "healthy": true, "response_time": 0.014746427536010742}, {"timestamp": "2025-07-30 16:43:30.419924", "healthy": true, "response_time": 0.015169143676757812}, {"timestamp": "2025-07-30 16:44:00.434433", "healthy": true, "response_time": 0.014397144317626953}, {"timestamp": "2025-07-30 16:44:31.173450", "healthy": true, "response_time": 0.014543771743774414}, {"timestamp": "2025-07-30 16:45:01.214744", "healthy": true, "response_time": 0.015021324157714844}, {"timestamp": "2025-07-30 16:45:31.256663", "healthy": true, "response_time": 0.016312837600708008}, {"timestamp": "2025-07-30 16:46:01.321418", "healthy": true, "response_time": 0.015799999237060547}, {"timestamp": "2025-07-30 16:46:31.317488", "healthy": true, "response_time": 0.015284299850463867}, {"timestamp": "2025-07-30 16:47:01.312854", "healthy": true, "response_time": 0.013501167297363281}, {"timestamp": "2025-07-30 16:47:31.314232", "healthy": true, "response_time": 0.015110969543457031}, {"timestamp": "2025-07-30 16:48:01.317323", "healthy": true, "response_time": 0.014859437942504883}, {"timestamp": "2025-07-30 16:48:31.371045", "healthy": true, "response_time": 0.014996528625488281}, {"timestamp": "2025-07-30 16:49:01.369943", "healthy": true, "response_time": 0.015773296356201172}, {"timestamp": "2025-07-30 16:49:31.378623", "healthy": true, "response_time": 0.014609575271606445}, {"timestamp": "2025-07-30 16:50:01.404508", "healthy": true, "response_time": 0.014338016510009766}, {"timestamp": "2025-07-30 16:50:31.415900", "healthy": true, "response_time": 0.015917301177978516}, {"timestamp": "2025-07-30 16:51:01.475600", "healthy": true, "response_time": 0.014729499816894531}, {"timestamp": "2025-07-30 16:51:31.481885", "healthy": true, "response_time": 0.014365434646606445}, {"timestamp": "2025-07-30 16:52:01.488899", "healthy": true, "response_time": 0.016874313354492188}, {"timestamp": "2025-07-30 16:52:31.493660", "healthy": true, "response_time": 0.014007091522216797}, {"timestamp": "2025-07-30 16:53:01.493106", "healthy": true, "response_time": 0.01619744300842285}, {"timestamp": "2025-07-30 16:53:31.550125", "healthy": true, "response_time": 0.014236927032470703}, {"timestamp": "2025-07-30 16:54:01.562177", "healthy": true, "response_time": 0.015243291854858398}, {"timestamp": "2025-07-30 16:54:31.564224", "healthy": true, "response_time": 0.014892578125}, {"timestamp": "2025-07-30 16:55:01.568783", "healthy": true, "response_time": 0.014802932739257812}, {"timestamp": "2025-07-30 16:55:31.571686", "healthy": true, "response_time": 0.01462864875793457}, {"timestamp": "2025-07-30 16:56:01.607171", "healthy": true, "response_time": 0.014592647552490234}, {"timestamp": "2025-07-30 16:56:31.599728", "healthy": true, "response_time": 0.014786958694458008}, {"timestamp": "2025-07-30 16:57:01.609310", "healthy": true, "response_time": 0.01591777801513672}, {"timestamp": "2025-07-30 16:57:31.612082", "healthy": true, "response_time": 0.014772415161132812}, {"timestamp": "2025-07-30 16:58:01.607844", "healthy": true, "response_time": 0.01449728012084961}, {"timestamp": "2025-07-30 16:58:31.647852", "healthy": true, "response_time": 0.015121936798095703}, {"timestamp": "2025-07-30 16:59:01.652898", "healthy": true, "response_time": 0.015062808990478516}, {"timestamp": "2025-07-30 16:59:31.647328", "healthy": true, "response_time": 0.016028404235839844}, {"timestamp": "2025-07-30 17:00:01.642563", "healthy": true, "response_time": 0.015074014663696289}, {"timestamp": "2025-07-30 17:00:31.638255", "healthy": true, "response_time": 0.015234231948852539}, {"timestamp": "2025-07-30 17:01:01.686259", "healthy": true, "response_time": 0.014704227447509766}, {"timestamp": "2025-07-30 17:01:31.686390", "healthy": true, "response_time": 0.014164447784423828}, {"timestamp": "2025-07-30 17:02:01.680643", "healthy": true, "response_time": 0.01406407356262207}, {"timestamp": "2025-07-30 17:02:31.850895", "healthy": true, "response_time": 0.015639305114746094}, {"timestamp": "2025-07-30 17:03:01.850627", "healthy": true, "response_time": 0.015031576156616211}, {"timestamp": "2025-07-30 17:03:31.876282", "healthy": true, "response_time": 0.014212846755981445}, {"timestamp": "2025-07-30 17:04:01.871851", "healthy": true, "response_time": 0.013794898986816406}, {"timestamp": "2025-07-30 17:04:31.877512", "healthy": true, "response_time": 0.013854026794433594}, {"timestamp": "2025-07-30 17:05:01.874554", "healthy": true, "response_time": 0.014462471008300781}, {"timestamp": "2025-07-30 17:05:31.860673", "healthy": true, "response_time": 0.014570951461791992}, {"timestamp": "2025-07-30 17:06:01.907827", "healthy": true, "response_time": 0.015599489212036133}], "http://************:11434": [{"timestamp": "2025-07-30 16:41:30.270866", "healthy": true, "response_time": 0.014612197875976562}, {"timestamp": "2025-07-30 16:42:00.276464", "healthy": true, "response_time": 0.018337488174438477}, {"timestamp": "2025-07-30 16:42:30.361886", "healthy": true, "response_time": 0.015197038650512695}, {"timestamp": "2025-07-30 16:43:00.382835", "healthy": true, "response_time": 0.015617132186889648}, {"timestamp": "2025-07-30 16:43:30.436617", "healthy": true, "response_time": 0.01664257049560547}, {"timestamp": "2025-07-30 16:44:00.728867", "healthy": true, "response_time": 0.2944018840789795}, {"timestamp": "2025-07-30 16:44:31.229170", "healthy": true, "response_time": 0.05568099021911621}, {"timestamp": "2025-07-30 16:45:01.267112", "healthy": true, "response_time": 0.0523374080657959}, {"timestamp": "2025-07-30 16:45:31.274014", "healthy": true, "response_time": 0.01731729507446289}, {"timestamp": "2025-07-30 16:46:01.338206", "healthy": true, "response_time": 0.016756057739257812}, {"timestamp": "2025-07-30 16:46:31.334045", "healthy": true, "response_time": 0.01652216911315918}, {"timestamp": "2025-07-30 16:47:01.329521", "healthy": true, "response_time": 0.01663661003112793}, {"timestamp": "2025-07-30 16:47:31.328221", "healthy": true, "response_time": 0.013945817947387695}, {"timestamp": "2025-07-30 16:48:01.332802", "healthy": true, "response_time": 0.015443086624145508}, {"timestamp": "2025-07-30 16:48:31.385296", "healthy": true, "response_time": 0.014214515686035156}, {"timestamp": "2025-07-30 16:49:01.385481", "healthy": true, "response_time": 0.015504121780395508}, {"timestamp": "2025-07-30 16:49:31.398168", "healthy": true, "response_time": 0.019506216049194336}, {"timestamp": "2025-07-30 16:50:01.420744", "healthy": true, "response_time": 0.016202688217163086}, {"timestamp": "2025-07-30 16:50:31.433250", "healthy": true, "response_time": 0.0173187255859375}, {"timestamp": "2025-07-30 16:51:01.490116", "healthy": true, "response_time": 0.014482975006103516}, {"timestamp": "2025-07-30 16:51:31.495831", "healthy": true, "response_time": 0.013912439346313477}, {"timestamp": "2025-07-30 16:52:01.505171", "healthy": true, "response_time": 0.016237735748291016}, {"timestamp": "2025-07-30 16:52:31.509079", "healthy": true, "response_time": 0.015386343002319336}, {"timestamp": "2025-07-30 16:53:01.510883", "healthy": true, "response_time": 0.017734527587890625}, {"timestamp": "2025-07-30 16:53:31.565152", "healthy": true, "response_time": 0.01499795913696289}, {"timestamp": "2025-07-30 16:54:01.577670", "healthy": true, "response_time": 0.01546025276184082}, {"timestamp": "2025-07-30 16:54:31.580127", "healthy": true, "response_time": 0.015872955322265625}, {"timestamp": "2025-07-30 16:55:01.583235", "healthy": true, "response_time": 0.014417409896850586}, {"timestamp": "2025-07-30 16:55:31.586923", "healthy": true, "response_time": 0.015203237533569336}, {"timestamp": "2025-07-30 16:56:01.625074", "healthy": true, "response_time": 0.017871856689453125}, {"timestamp": "2025-07-30 16:56:31.615614", "healthy": true, "response_time": 0.015856027603149414}, {"timestamp": "2025-07-30 16:57:01.624780", "healthy": true, "response_time": 0.015439033508300781}, {"timestamp": "2025-07-30 16:57:31.626567", "healthy": true, "response_time": 0.014450311660766602}, {"timestamp": "2025-07-30 16:58:01.623584", "healthy": true, "response_time": 0.015708446502685547}, {"timestamp": "2025-07-30 16:58:31.662571", "healthy": true, "response_time": 0.01468515396118164}, {"timestamp": "2025-07-30 16:59:01.668014", "healthy": true, "response_time": 0.01508474349975586}, {"timestamp": "2025-07-30 16:59:31.661757", "healthy": true, "response_time": 0.014389753341674805}, {"timestamp": "2025-07-30 17:00:01.661574", "healthy": true, "response_time": 0.018973350524902344}, {"timestamp": "2025-07-30 17:00:31.652866", "healthy": true, "response_time": 0.014579534530639648}, {"timestamp": "2025-07-30 17:01:01.702095", "healthy": true, "response_time": 0.01580214500427246}, {"timestamp": "2025-07-30 17:01:31.701576", "healthy": true, "response_time": 0.015149831771850586}, {"timestamp": "2025-07-30 17:02:01.863428", "healthy": true, "response_time": 0.1827540397644043}, {"timestamp": "2025-07-30 17:02:31.865835", "healthy": true, "response_time": 0.014909029006958008}, {"timestamp": "2025-07-30 17:03:01.866278", "healthy": true, "response_time": 0.015617847442626953}, {"timestamp": "2025-07-30 17:03:31.891631", "healthy": true, "response_time": 0.015298604965209961}, {"timestamp": "2025-07-30 17:04:01.890375", "healthy": true, "response_time": 0.01849365234375}, {"timestamp": "2025-07-30 17:04:31.892323", "healthy": true, "response_time": 0.01477503776550293}, {"timestamp": "2025-07-30 17:05:01.889750", "healthy": true, "response_time": 0.015166044235229492}, {"timestamp": "2025-07-30 17:05:31.875451", "healthy": true, "response_time": 0.014740943908691406}, {"timestamp": "2025-07-30 17:06:02.083684", "healthy": true, "response_time": 0.1758253574371338}], "http://************:11434": [{"timestamp": "2025-07-30 16:43:00.396767", "healthy": true, "response_time": 0.01390528678894043}, {"timestamp": "2025-07-30 16:43:30.452049", "healthy": true, "response_time": 0.015393972396850586}, {"timestamp": "2025-07-30 16:44:00.744450", "healthy": true, "response_time": 0.015552282333374023}, {"timestamp": "2025-07-30 16:44:31.242510", "healthy": true, "response_time": 0.013308286666870117}, {"timestamp": "2025-07-30 16:45:01.283572", "healthy": true, "response_time": 0.016422748565673828}, {"timestamp": "2025-07-30 16:45:31.287694", "healthy": true, "response_time": 0.013644933700561523}, {"timestamp": "2025-07-30 16:45:57.735508", "healthy": true, "response_time": 25.014324426651}, {"timestamp": "2025-07-30 16:46:01.350308", "healthy": true, "response_time": 0.012072563171386719}, {"timestamp": "2025-07-30 16:46:31.346864", "healthy": true, "response_time": 0.01279306411743164}, {"timestamp": "2025-07-30 16:47:01.345271", "healthy": true, "response_time": 0.01563739776611328}, {"timestamp": "2025-07-30 16:47:31.342186", "healthy": true, "response_time": 0.013932466506958008}, {"timestamp": "2025-07-30 16:48:01.347511", "healthy": true, "response_time": 0.014675140380859375}, {"timestamp": "2025-07-30 16:48:31.399696", "healthy": true, "response_time": 0.014369487762451172}, {"timestamp": "2025-07-30 16:49:01.401100", "healthy": true, "response_time": 0.015588045120239258}, {"timestamp": "2025-07-30 16:49:23.486209", "healthy": true, "response_time": 13.178094387054443}, {"timestamp": "2025-07-30 16:49:31.421618", "healthy": true, "response_time": 0.02342057228088379}, {"timestamp": "2025-07-30 16:50:01.438774", "healthy": true, "response_time": 0.017987489700317383}, {"timestamp": "2025-07-30 16:50:31.448200", "healthy": true, "response_time": 0.014917373657226562}, {"timestamp": "2025-07-30 16:51:01.505757", "healthy": true, "response_time": 0.01560664176940918}, {"timestamp": "2025-07-30 16:51:25.183889", "healthy": true, "response_time": 15.15416431427002}, {"timestamp": "2025-07-30 16:51:31.509984", "healthy": true, "response_time": 0.014115571975708008}, {"timestamp": "2025-07-30 16:52:01.521702", "healthy": true, "response_time": 0.0164949893951416}, {"timestamp": "2025-07-30 16:52:31.522729", "healthy": true, "response_time": 0.013623952865600586}, {"timestamp": "2025-07-30 16:53:01.527393", "healthy": true, "response_time": 0.016477584838867188}, {"timestamp": "2025-07-30 16:53:31.579416", "healthy": true, "response_time": 0.014236211776733398}, {"timestamp": "2025-07-30 16:54:01.592534", "healthy": true, "response_time": 0.014835357666015625}, {"timestamp": "2025-07-30 16:54:31.594193", "healthy": true, "response_time": 0.014031410217285156}, {"timestamp": "2025-07-30 16:55:01.596323", "healthy": true, "response_time": 0.013058662414550781}, {"timestamp": "2025-07-30 16:55:31.600362", "healthy": true, "response_time": 0.013407468795776367}, {"timestamp": "2025-07-30 16:56:01.640933", "healthy": true, "response_time": 0.01582646369934082}, {"timestamp": "2025-07-30 16:56:31.629975", "healthy": true, "response_time": 0.014321565628051758}, {"timestamp": "2025-07-30 16:57:01.641294", "healthy": true, "response_time": 0.016483068466186523}, {"timestamp": "2025-07-30 16:57:31.640553", "healthy": true, "response_time": 0.013952970504760742}, {"timestamp": "2025-07-30 16:58:01.638866", "healthy": true, "response_time": 0.015252113342285156}, {"timestamp": "2025-07-30 16:58:31.677215", "healthy": true, "response_time": 0.014603137969970703}, {"timestamp": "2025-07-30 16:59:01.682715", "healthy": true, "response_time": 0.014664888381958008}, {"timestamp": "2025-07-30 16:59:31.675040", "healthy": true, "response_time": 0.013249874114990234}, {"timestamp": "2025-07-30 17:00:01.677304", "healthy": true, "response_time": 0.01569652557373047}, {"timestamp": "2025-07-30 17:00:31.666998", "healthy": true, "response_time": 0.014103412628173828}, {"timestamp": "2025-07-30 17:01:01.716953", "healthy": true, "response_time": 0.014830827713012695}, {"timestamp": "2025-07-30 17:01:31.715678", "healthy": true, "response_time": 0.014075279235839844}, {"timestamp": "2025-07-30 17:02:01.878056", "healthy": true, "response_time": 0.014594793319702148}, {"timestamp": "2025-07-30 17:02:31.879792", "healthy": true, "response_time": 0.013923883438110352}, {"timestamp": "2025-07-30 17:03:01.881592", "healthy": true, "response_time": 0.015254974365234375}, {"timestamp": "2025-07-30 17:03:31.905127", "healthy": true, "response_time": 0.013463735580444336}, {"timestamp": "2025-07-30 17:04:01.904928", "healthy": true, "response_time": 0.01452183723449707}, {"timestamp": "2025-07-30 17:04:31.905862", "healthy": true, "response_time": 0.013510704040527344}, {"timestamp": "2025-07-30 17:05:01.904765", "healthy": true, "response_time": 0.014983177185058594}, {"timestamp": "2025-07-30 17:05:31.889509", "healthy": true, "response_time": 0.01401376724243164}, {"timestamp": "2025-07-30 17:06:02.098723", "healthy": true, "response_time": 0.015002727508544922}], "http://************:11434": [{"timestamp": "2025-07-30 16:41:30.306538", "healthy": true, "response_time": 0.019548654556274414}, {"timestamp": "2025-07-30 16:42:00.312557", "healthy": true, "response_time": 0.01888275146484375}, {"timestamp": "2025-07-30 16:42:30.398345", "healthy": true, "response_time": 0.0181121826171875}, {"timestamp": "2025-07-30 16:43:00.413992", "healthy": true, "response_time": 0.017197370529174805}, {"timestamp": "2025-07-30 16:43:30.476836", "healthy": true, "response_time": 0.024756431579589844}, {"timestamp": "2025-07-30 16:44:01.211664", "healthy": true, "response_time": 0.46718311309814453}, {"timestamp": "2025-07-30 16:44:31.261655", "healthy": true, "response_time": 0.019108295440673828}, {"timestamp": "2025-07-30 16:45:01.302405", "healthy": true, "response_time": 0.01879405975341797}, {"timestamp": "2025-07-30 16:45:31.307985", "healthy": true, "response_time": 0.02025318145751953}, {"timestamp": "2025-07-30 16:46:01.368970", "healthy": true, "response_time": 0.018641233444213867}, {"timestamp": "2025-07-30 16:46:31.371625", "healthy": true, "response_time": 0.024719715118408203}, {"timestamp": "2025-07-30 16:47:01.365746", "healthy": true, "response_time": 0.020441055297851562}, {"timestamp": "2025-07-30 16:47:31.359769", "healthy": true, "response_time": 0.017550945281982422}, {"timestamp": "2025-07-30 16:48:01.366017", "healthy": true, "response_time": 0.018472671508789062}, {"timestamp": "2025-07-30 16:48:31.417380", "healthy": true, "response_time": 0.017653942108154297}, {"timestamp": "2025-07-30 16:49:01.423712", "healthy": true, "response_time": 0.022580385208129883}, {"timestamp": "2025-07-30 16:49:31.450693", "healthy": true, "response_time": 0.029040813446044922}, {"timestamp": "2025-07-30 16:50:01.460421", "healthy": true, "response_time": 0.021604061126708984}, {"timestamp": "2025-07-30 16:50:31.472021", "healthy": true, "response_time": 0.02374863624572754}, {"timestamp": "2025-07-30 16:51:01.525855", "healthy": true, "response_time": 0.02006697654724121}, {"timestamp": "2025-07-30 16:51:31.531750", "healthy": true, "response_time": 0.021738290786743164}, {"timestamp": "2025-07-30 16:52:01.541185", "healthy": true, "response_time": 0.01944446563720703}, {"timestamp": "2025-07-30 16:52:31.542932", "healthy": true, "response_time": 0.02017354965209961}, {"timestamp": "2025-07-30 16:53:01.545507", "healthy": true, "response_time": 0.018078327178955078}, {"timestamp": "2025-07-30 16:53:31.604527", "healthy": true, "response_time": 0.02508711814880371}, {"timestamp": "2025-07-30 16:54:01.610146", "healthy": true, "response_time": 0.017585039138793945}, {"timestamp": "2025-07-30 16:54:31.611711", "healthy": true, "response_time": 0.017491817474365234}, {"timestamp": "2025-07-30 16:55:01.613060", "healthy": true, "response_time": 0.016704797744750977}, {"timestamp": "2025-07-30 16:55:31.618017", "healthy": true, "response_time": 0.017608642578125}, {"timestamp": "2025-07-30 16:56:01.658108", "healthy": true, "response_time": 0.017146587371826172}, {"timestamp": "2025-07-30 16:56:31.651861", "healthy": true, "response_time": 0.021845340728759766}, {"timestamp": "2025-07-30 16:57:01.657987", "healthy": true, "response_time": 0.016661882400512695}, {"timestamp": "2025-07-30 16:57:31.657715", "healthy": true, "response_time": 0.01712942123413086}, {"timestamp": "2025-07-30 16:58:01.658249", "healthy": true, "response_time": 0.019351720809936523}, {"timestamp": "2025-07-30 16:58:31.696434", "healthy": true, "response_time": 0.019182920455932617}, {"timestamp": "2025-07-30 16:59:01.700849", "healthy": true, "response_time": 0.018090248107910156}, {"timestamp": "2025-07-30 16:59:31.695460", "healthy": true, "response_time": 0.020390748977661133}, {"timestamp": "2025-07-30 17:00:01.695794", "healthy": true, "response_time": 0.01845240592956543}, {"timestamp": "2025-07-30 17:00:31.686325", "healthy": true, "response_time": 0.01929306983947754}, {"timestamp": "2025-07-30 17:01:01.733292", "healthy": true, "response_time": 0.016314268112182617}, {"timestamp": "2025-07-30 17:01:31.733371", "healthy": true, "response_time": 0.017664194107055664}, {"timestamp": "2025-07-30 17:02:01.896880", "healthy": true, "response_time": 0.01879405975341797}, {"timestamp": "2025-07-30 17:02:31.896627", "healthy": true, "response_time": 0.016807079315185547}, {"timestamp": "2025-07-30 17:03:01.901427", "healthy": true, "response_time": 0.019797801971435547}, {"timestamp": "2025-07-30 17:03:31.923855", "healthy": true, "response_time": 0.01870274543762207}, {"timestamp": "2025-07-30 17:04:01.929968", "healthy": true, "response_time": 0.025010347366333008}, {"timestamp": "2025-07-30 17:04:31.927218", "healthy": true, "response_time": 0.021326303482055664}, {"timestamp": "2025-07-30 17:05:01.922502", "healthy": true, "response_time": 0.01771402359008789}, {"timestamp": "2025-07-30 17:05:31.915521", "healthy": true, "response_time": 0.02597665786743164}, {"timestamp": "2025-07-30 17:06:02.117327", "healthy": true, "response_time": 0.018571853637695312}]}, "total_prompt_tokens": 5414, "total_completion_tokens": 49051, "total_tokens": 54465, "total_prompt_eval_duration": 48582420845.0, "total_eval_duration": 1734422180698.0, "model_token_stats": {"llama3:latest": {"prompt_tokens": 880, "completion_tokens": 13772, "total_tokens": 14652, "prompt_eval_duration": 30199793472.0, "eval_duration": 957023646343.0, "prompt_tokens_per_sec": 29.13927212170837, "completion_tokens_per_sec": 14.390449026652448}, "llama3.2:3b": {"prompt_tokens": 229, "completion_tokens": 1022, "total_tokens": 1251, "prompt_eval_duration": 816971059.0, "eval_duration": 28562354546.0, "prompt_tokens_per_sec": 280.30368698776635, "completion_tokens_per_sec": 35.78136383518583}, "granite3.3:8b": {"prompt_tokens": 137, "completion_tokens": 235, "total_tokens": 372, "prompt_eval_duration": **********.0, "eval_duration": 11178358043.0, "prompt_tokens_per_sec": 107.26600273640898, "completion_tokens_per_sec": 21.022765516726256}, "tinyllama:latest": {"prompt_tokens": 2665, "completion_tokens": 6850, "total_tokens": 9515, "prompt_eval_duration": 4193504881.0, "eval_duration": 85181190912.0, "prompt_tokens_per_sec": 635.506593082704, "completion_tokens_per_sec": 80.41681416589584}, "dolphin-mistral:7b": {"prompt_tokens": 62, "completion_tokens": 37, "total_tokens": 99, "prompt_eval_duration": 1072755671.0, "eval_duration": 1210160326.0, "prompt_tokens_per_sec": 57.79508016229355, "completion_tokens_per_sec": 30.574461255309735}, "deepseek-r1:1.5b": {"prompt_tokens": 114, "completion_tokens": 4709, "total_tokens": 4823, "prompt_eval_duration": 1655588100.0, "eval_duration": 118808886900.0, "prompt_tokens_per_sec": 68.8577068172935, "completion_tokens_per_sec": 39.63508221370265}, "phi:latest": {"prompt_tokens": 753, "completion_tokens": 901, "total_tokens": 1654, "prompt_eval_duration": 3762097674.0, "eval_duration": 12586802062.0, "prompt_tokens_per_sec": 200.15429296373978, "completion_tokens_per_sec": 71.5829164200612}, "smollm2:1.7b": {"prompt_tokens": 60, "completion_tokens": 33, "total_tokens": 93, "prompt_eval_duration": 308165237.0, "eval_duration": 465713846.0, "prompt_tokens_per_sec": 194.70074101836477, "completion_tokens_per_sec": 70.85896260855426}, "smollm:135m": {"prompt_tokens": 20, "completion_tokens": 19, "total_tokens": 39, "prompt_eval_duration": 72452995.0, "eval_duration": 124512954.0, "prompt_tokens_per_sec": 276.0410387451892, "completion_tokens_per_sec": 152.5945645783972}, "smollm2:latest": {"prompt_tokens": 30, "completion_tokens": 10, "total_tokens": 40, "prompt_eval_duration": 168421500.0, "eval_duration": 58118500.0, "prompt_tokens_per_sec": 178.12452685672554, "completion_tokens_per_sec": 172.06225212281805}, "llama3.1:latest": {"prompt_tokens": 11, "completion_tokens": 21, "total_tokens": 32, "prompt_eval_duration": 183495400.0, "eval_duration": 225529000.0, "prompt_tokens_per_sec": 59.94700684594818, "completion_tokens_per_sec": 93.11441100701018}, "gemma3:latest": {"prompt_tokens": 147, "completion_tokens": 4245, "total_tokens": 4392, "prompt_eval_duration": 1432854900.0, "eval_duration": 40967382200.0, "prompt_tokens_per_sec": 102.5923839182879, "completion_tokens_per_sec": 103.61902010912476}, "codellama:7b": {"prompt_tokens": 32, "completion_tokens": 268, "total_tokens": 300, "prompt_eval_duration": 737511145.0, "eval_duration": 8617590101.0, "prompt_tokens_per_sec": 43.38917481714802, "completion_tokens_per_sec": 31.099181657398724}, "codellama:7b-instruct": {"prompt_tokens": 32, "completion_tokens": 548, "total_tokens": 580, "prompt_eval_duration": 8119568.0, "eval_duration": 18508394671.0, "prompt_tokens_per_sec": 3941.0963736001713, "completion_tokens_per_sec": 29.608186433296527}, "Deepcoder:latest": {"prompt_tokens": 15, "completion_tokens": 2688, "total_tokens": 2703, "prompt_eval_duration": 927240750.0, "eval_duration": 262208681152.0, "prompt_tokens_per_sec": 16.177028457819613, "completion_tokens_per_sec": 10.251376835390857}, "deepseek-coder:6.7b": {"prompt_tokens": 81, "completion_tokens": 579, "total_tokens": 660, "prompt_eval_duration": 1122318886.0, "eval_duration": 19895651542.0, "prompt_tokens_per_sec": 72.17200121142754, "completion_tokens_per_sec": 29.10183658864968}, "deepseek-r1:8b": {"prompt_tokens": 146, "completion_tokens": 13114, "total_tokens": 13260, "prompt_eval_duration": 643930900.0, "eval_duration": 168799207600.0, "prompt_tokens_per_sec": 196.12082497908705, "completion_tokens_per_sec": 77.4435846686897}}, "host_token_stats": {"http://************:11434": {"prompt_tokens": 1045, "completion_tokens": 6431, "total_tokens": 7476, "prompt_eval_duration": 7345138000.0, "eval_duration": 239199298200.0, "prompt_tokens_per_sec": 142.27098251932094, "completion_tokens_per_sec": 26.885530385724184}, "http://************:11434": {"prompt_tokens": 922, "completion_tokens": 7391, "total_tokens": 8313, "prompt_eval_duration": 8900366745.0, "eval_duration": 418508522698.0, "prompt_tokens_per_sec": 103.59123690245194, "completion_tokens_per_sec": 17.660333300627716}, "http://************:11434": {"prompt_tokens": 891, "completion_tokens": 5292, "total_tokens": 6183, "prompt_eval_duration": 10843531700.0, "eval_duration": 243854355200.0, "prompt_tokens_per_sec": 82.16880114806139, "completion_tokens_per_sec": 21.701478309295336}, "http://************:11434": {"prompt_tokens": 972, "completion_tokens": 23435, "total_tokens": 24407, "prompt_eval_duration": 5458645600.0, "eval_duration": 272497541500.0, "prompt_tokens_per_sec": 174.40863539379083, "completion_tokens_per_sec": 86.3389959883824}}}