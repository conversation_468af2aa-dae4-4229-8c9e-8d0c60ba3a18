{"data_mtime": 1753889303, "dep_lines": [10, 17, 10, 11, 11, 9, 5, 7, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 20, 10, 20, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.backends.openssl.backend", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.primitives._serialization", "cryptography.hazmat.primitives", "cryptography.exceptions", "__future__", "abc", "builtins", "_frozen_importlib", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust.openssl.x25519", "cryptography.utils", "enum", "typing"], "hash": "79df2e91386bd25d3b6cacf3e1ebf09516a30c1a", "id": "cryptography.hazmat.primitives.asymmetric.x25519", "ignore_all": true, "interface_hash": "9c33d324de310fb6ec664156a799eab3cbcc97a0", "mtime": 1708667824, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py", "plugin_data": null, "size": 3341, "suppressed": [], "version_id": "1.15.0"}