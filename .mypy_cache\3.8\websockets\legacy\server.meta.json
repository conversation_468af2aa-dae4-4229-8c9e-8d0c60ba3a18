{"data_mtime": 1753889308, "dep_lines": [15, 26, 36, 37, 38, 39, 4, 11, 16, 17, 25, 27, 33, 34, 35, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.asyncio.compatibility", "websockets.extensions.permessage_deflate", "websockets.legacy.exceptions", "websockets.legacy.handshake", "websockets.legacy.http", "websockets.legacy.protocol", "email.utils", "collections.abc", "websockets.datastructures", "websockets.exceptions", "websockets.extensions", "websockets.headers", "websockets.http11", "websockets.protocol", "websockets.typing", "__future__", "asyncio", "email", "functools", "http", "inspect", "logging", "socket", "warnings", "types", "typing", "builtins", "_asyncio", "_frozen_importlib", "_socket", "_ssl", "abc", "asyncio.base_events", "asyncio.events", "asyncio.exceptions", "asyncio.protocols", "asyncio.streams", "asyncio.transports", "enum", "os", "ssl", "typing_extensions", "websockets.extensions.base"], "hash": "3eefcb573bb51adcbfbb1ad7e2c26de89dd22877", "id": "websockets.legacy.server", "ignore_all": true, "interface_hash": "d63be9649a6d973d7ed9a68cff573f0cbae8b1d9", "mtime": 1750470646, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\websockets\\legacy\\server.py", "plugin_data": null, "size": 46441, "suppressed": [], "version_id": "1.15.0"}