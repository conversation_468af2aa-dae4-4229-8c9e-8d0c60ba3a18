{"data_mtime": 1753889307, "dep_lines": [7, 8, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.datastructures", "websockets.exceptions", "__future__", "asyncio", "os", "re", "builtins", "_frozen_importlib", "_typeshed", "abc", "asyncio.streams", "enum", "typing", "typing_extensions"], "hash": "57abb98d36de98582f1e2acd25291e929704103e", "id": "websockets.legacy.http", "ignore_all": true, "interface_hash": "aa2dce736d47f233598ee9e0e6d680c609a0de3c", "mtime": 1750470646, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\websockets\\legacy\\http.py", "plugin_data": null, "size": 7262, "suppressed": [], "version_id": "1.15.0"}