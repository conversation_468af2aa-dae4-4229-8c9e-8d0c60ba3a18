{".class": "MypyFile", "_fullname": "werkzeug.sansio.multipart", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BLANK_LINE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.BLANK_LINE_RE", "name": "BLANK_LINE_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.sansio.multipart.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.Data", "name": "Data", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Data", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "data", "type": "builtins.bytes"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "more_data", "type": "builtins.bool"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.Data", "werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "werkzeug.sansio.multipart.Data.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "more_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Data.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "more_data"], "arg_types": ["werkzeug.sansio.multipart.Data", "builtins.bytes", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Data", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["data", "more_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "werkzeug.sansio.multipart.Data.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["data", "more_data"], "arg_types": ["builtins.bytes", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Data", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "werkzeug.sansio.multipart.Data.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["data", "more_data"], "arg_types": ["builtins.bytes", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Data", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.Data.data", "name": "data", "type": "builtins.bytes"}}, "more_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.Data.more_data", "name": "more_data", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.Data.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.Data", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Epilogue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.sansio.multipart.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.Epilogue", "name": "Epilogue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Epilogue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "data", "type": "builtins.bytes"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.Epilogue", "werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "werkzeug.sansio.multipart.Epilogue.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Epilogue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["werkzeug.sansio.multipart.Epilogue", "builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Epilogue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "werkzeug.sansio.multipart.Epilogue.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Epilogue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "werkzeug.sansio.multipart.Epilogue.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Epilogue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.Epilogue.data", "name": "data", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.Epilogue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.Epilogue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Event": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.Event", "name": "Event", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Event", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.Event.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.Event", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.sansio.multipart.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.Field", "name": "Field", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Field", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 25, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 26, "name": "headers", "type": "werkzeug.datastructures.headers.Headers"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.Field", "werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "werkzeug.sansio.multipart.Field.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Field.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "headers"], "arg_types": ["werkzeug.sansio.multipart.Field", "builtins.str", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Field", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["name", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "werkzeug.sansio.multipart.Field.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["name", "headers"], "arg_types": ["builtins.str", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "werkzeug.sansio.multipart.Field.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["name", "headers"], "arg_types": ["builtins.str", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.Field.headers", "name": "headers", "type": "werkzeug.datastructures.headers.Headers"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.Field.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.Field.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.Field", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "File": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.sansio.multipart.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.File", "name": "File", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.File", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "filename", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "headers", "type": "werkzeug.datastructures.headers.Headers"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.File", "werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "werkzeug.sansio.multipart.File.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "filename", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "filename", "headers"], "arg_types": ["werkzeug.sansio.multipart.File", "builtins.str", "builtins.str", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["name", "filename", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "werkzeug.sansio.multipart.File.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["name", "filename", "headers"], "arg_types": ["builtins.str", "builtins.str", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "werkzeug.sansio.multipart.File.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["name", "filename", "headers"], "arg_types": ["builtins.str", "builtins.str", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.File.filename", "name": "filename", "type": "builtins.str"}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.File.headers", "name": "headers", "type": "werkzeug.datastructures.headers.Headers"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.File.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.File", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HEADER_CONTINUATION_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.HEADER_CONTINUATION_RE", "name": "HEADER_CONTINUATION_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.headers.Headers", "kind": "Gdef"}, "LINE_BREAK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.LINE_BREAK", "name": "LINE_BREAK", "type": "builtins.bytes"}}, "LINE_BREAK_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.LINE_BREAK_RE", "name": "LINE_BREAK_RE", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "MultipartDecoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.MultipartDecoder", "name": "MultipartDecoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.MultipartDecoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "boundary", "max_form_memory_size", "max_parts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "boundary", "max_form_memory_size", "max_parts"], "arg_types": ["werkzeug.sansio.multipart.MultipartDecoder", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartDecoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "data", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder._parse_data", "name": "_parse_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "data", "start"], "arg_types": ["werkzeug.sansio.multipart.MultipartDecoder", "builtins.bytes", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_data of MultipartDecoder", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder._parse_headers", "name": "_parse_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["werkzeug.sansio.multipart.MultipartDecoder", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_headers of MultipartDecoder", "ret_type": "werkzeug.datastructures.headers.Headers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parts_decoded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder._parts_decoded", "name": "_parts_decoded", "type": "builtins.int"}}, "_search_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder._search_position", "name": "_search_position", "type": "builtins.int"}}, "boundary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.boundary", "name": "boundary", "type": "builtins.bytes"}}, "boundary_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.boundary_re", "name": "boundary_re", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.buffer", "name": "buffer", "type": "builtins.bytearray"}}, "complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.complete", "name": "complete", "type": "builtins.bool"}}, "last_newline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.last_newline", "name": "last_newline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["werkzeug.sansio.multipart.MultipartDecoder", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last_newline of MultipartDecoder", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_form_memory_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.max_form_memory_size", "name": "max_form_memory_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "max_parts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.max_parts", "name": "max_parts", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "next_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.next_event", "name": "next_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.sansio.multipart.MultipartDecoder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_event of MultipartDecoder", "ret_type": "werkzeug.sansio.multipart.Event", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preamble_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.preamble_re", "name": "preamble_re", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "receive_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.receive_data", "name": "receive_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["werkzeug.sansio.multipart.MultipartDecoder", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_data of MultipartDecoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartDecoder.state", "name": "state", "type": "werkzeug.sansio.multipart.State"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.MultipartDecoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.MultipartDecoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.MultipartEncoder", "name": "MultipartEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.MultipartEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "boundary"], "arg_types": ["werkzeug.sansio.multipart.MultipartEncoder", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "boundary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartEncoder.boundary", "name": "boundary", "type": "builtins.bytes"}}, "send_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.MultipartEncoder.send_event", "name": "send_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["werkzeug.sansio.multipart.MultipartEncoder", "werkzeug.sansio.multipart.Event"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_event of MultipartEncoder", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.sansio.multipart.MultipartEncoder.state", "name": "state", "type": "werkzeug.sansio.multipart.State"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.MultipartEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.MultipartEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NEED_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.NEED_DATA", "name": "NEED_DATA", "type": "werkzeug.sansio.multipart.NeedData"}}, "NeedData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.sansio.multipart.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.NeedData", "name": "NeedData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.NeedData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.NeedData", "werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.NeedData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.NeedData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Preamble": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["werkzeug.sansio.multipart.Event"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.Preamble", "name": "Preamble", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Preamble", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "data", "type": "builtins.bytes"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.Preamble", "werkzeug.sansio.multipart.Event", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "werkzeug.sansio.multipart.Preamble.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.sansio.multipart.Preamble.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["werkzeug.sansio.multipart.Preamble", "builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Preamble", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "werkzeug.sansio.multipart.Preamble.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Preamble", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "werkzeug.sansio.multipart.Preamble.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Preamble", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "werkzeug.sansio.multipart.Preamble.data", "name": "data", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.Preamble.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.Preamble", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestEntityTooLarge": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.RequestEntityTooLarge", "kind": "Gdef"}, "SEARCH_EXTRA_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.SEARCH_EXTRA_LENGTH", "name": "SEARCH_EXTRA_LENGTH", "type": "builtins.int"}}, "State": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.sansio.multipart.State", "name": "State", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "werkzeug.sansio.multipart.State", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "werkzeug.sansio.multipart", "mro": ["werkzeug.sansio.multipart.State", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "COMPLETE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.State.COMPLETE", "name": "COMPLETE", "type": "enum.auto"}}, "DATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.State.DATA", "name": "DATA", "type": "enum.auto"}}, "DATA_START": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.State.DATA_START", "name": "DATA_START", "type": "enum.auto"}}, "EPILOGUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.State.EPILOGUE", "name": "EPILOGUE", "type": "enum.auto"}}, "PART": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.State.PART", "name": "PART", "type": "enum.auto"}}, "PREAMBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.sansio.multipart.State.PREAMBLE", "name": "PREAMBLE", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.sansio.multipart.State.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.sansio.multipart.State", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.sansio.multipart.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.sansio.multipart.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.sansio.multipart.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.sansio.multipart.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.sansio.multipart.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.sansio.multipart.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "auto": {".class": "SymbolTableNode", "cross_ref": "enum.auto", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "parse_options_header": {".class": "SymbolTableNode", "cross_ref": "werkzeug.http.parse_options_header", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\sansio\\multipart.py"}