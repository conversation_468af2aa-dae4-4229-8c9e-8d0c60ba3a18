{"data_mtime": 1753889309, "dep_lines": [11, 14, 15, 16, 35, 6, 8, 11, 12, 13, 17, 18, 20, 33, 34, 42, 1, 3, 4, 5, 7, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["websockets.legacy.handshake", "websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.legacy.server", "uvicorn.protocols.utils", "collections.abc", "urllib.parse", "websockets.legacy", "websockets.datastructures", "websockets.exceptions", "websockets.server", "websockets.typing", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "http", "logging", "typing", "websockets", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.events", "asyncio.locks", "asyncio.protocols", "asyncio.transports", "enum", "types", "typing_extensions", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.wsproto_impl", "websockets.extensions", "websockets.frames"], "hash": "e37d9c00f7f659c2bb1a489ab5c4a638638b40fb", "id": "uvicorn.protocols.websockets.websockets_impl", "ignore_all": true, "interface_hash": "5662ecddb5c27a227695b7edf88ae1faad104c36", "mtime": 1750470764, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py", "plugin_data": null, "size": 15504, "suppressed": [], "version_id": "1.15.0"}