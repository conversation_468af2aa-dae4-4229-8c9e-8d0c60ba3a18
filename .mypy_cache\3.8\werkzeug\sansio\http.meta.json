{"data_mtime": 1753889304, "dep_lines": [7, 8, 171, 1, 3, 4, 5, 171, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["werkzeug._internal", "werkzeug.http", "werkzeug.datastructures", "__future__", "re", "typing", "datetime", "werkzeug", "builtins", "_frozen_importlib", "abc", "enum", "werkzeug.datastructures.structures"], "hash": "cde4c331f2c58140fd0eb3844b77393e592f6ceb", "id": "werkzeug.sansio.http", "ignore_all": true, "interface_hash": "0b36d3183371b5ab3d22aadfec242f8a86c14524", "mtime": 1708667571, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\sansio\\http.py", "plugin_data": null, "size": 5320, "suppressed": [], "version_id": "1.15.0"}