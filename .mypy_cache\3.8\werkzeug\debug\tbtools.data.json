{".class": "MypyFile", "_fullname": "werkzeug.debug.tbtools", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CONSOLE_HTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.tbtools.CONSOLE_HTML", "name": "CONSOLE_HTML", "type": "builtins.str"}}, "Console": {".class": "SymbolTableNode", "cross_ref": "werkzeug.debug.console.Console", "kind": "Gdef"}, "DebugFrameSummary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traceback.FrameSummary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.debug.tbtools.DebugFrameSummary", "name": "DebugFrameSummary", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.debug.tbtools", "mro": ["werkzeug.debug.tbtools.DebugFrameSummary", "traceback.FrameSummary", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 4], "arg_names": ["self", "locals", "globals", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 4], "arg_names": ["self", "locals", "globals", "kwargs"], "arg_types": ["werkzeug.debug.tbtools.DebugFrameSummary", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DebugFrameSummary", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "console": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.console", "name": "console", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.tbtools.DebugFrameSummary"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "console of DebugFrameSummary", "ret_type": "werkzeug.debug.console.Console", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.console", "name": "console", "type": {".class": "Instance", "args": ["werkzeug.debug.console.Console"], "extra_attrs": null, "type_ref": "werkzeug.utils.cached_property"}}}}, "eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.eval", "name": "eval", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["werkzeug.debug.tbtools.DebugFrameSummary", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eval of DebugFrameSummary", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "global_ns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.global_ns", "name": "global_ns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.tbtools.DebugFrameSummary"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of DebugFrameSummary", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.info", "name": "info", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "werkzeug.utils.cached_property"}}}}, "is_library": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.is_library", "name": "is_library", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.tbtools.DebugFrameSummary"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_library of DebugFrameSummary", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.is_library", "name": "is_library", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "werkzeug.utils.cached_property"}}}}, "local_ns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.local_ns", "name": "local_ns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "render_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mark_library"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.render_html", "name": "render_html", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mark_library"], "arg_types": ["werkzeug.debug.tbtools.DebugFrameSummary", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_html of DebugFrameSummary", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.debug.tbtools.DebugFrameSummary.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.debug.tbtools.DebugFrameSummary", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DebugTraceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.debug.tbtools.DebugTraceback", "name": "DebugTraceback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugTraceback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.debug.tbtools", "mro": ["werkzeug.debug.tbtools.DebugTraceback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "exc", "te", "skip", "hide"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugTraceback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "exc", "te", "skip", "hide"], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback", "builtins.BaseException", {".class": "UnionType", "items": ["traceback.TracebackException", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DebugTraceback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "werkzeug.debug.tbtools.DebugTraceback.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugTraceback.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of DebugTraceback", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_te": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugTraceback._te", "name": "_te", "type": "traceback.TracebackException"}}, "all_frames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "werkzeug.debug.tbtools.DebugTraceback.all_frames", "name": "all_frames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_frames of DebugTraceback", "ret_type": {".class": "Instance", "args": ["werkzeug.debug.tbtools.DebugFrameSummary"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugTraceback.all_frames", "name": "all_frames", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["werkzeug.debug.tbtools.DebugFrameSummary"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "werkzeug.utils.cached_property"}}}}, "all_tracebacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "werkzeug.debug.tbtools.DebugTraceback.all_tracebacks", "name": "all_tracebacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_tracebacks of DebugTraceback", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "traceback.TracebackException"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.tbtools.DebugTraceback.all_tracebacks", "name": "all_tracebacks", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "traceback.TracebackException"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "werkzeug.utils.cached_property"}}}}, "render_debugger_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evalex", "secret", "evalex_trusted"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugTraceback.render_debugger_html", "name": "render_debugger_html", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "evalex", "secret", "evalex_trusted"], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_debugger_html of DebugTraceback", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_traceback_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_title"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugTraceback.render_traceback_html", "name": "render_traceback_html", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_title"], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_traceback_html of DebugTraceback", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_traceback_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.DebugTraceback.render_traceback_text", "name": "render_traceback_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.tbtools.DebugTraceback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_traceback_text of DebugTraceback", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.debug.tbtools.DebugTraceback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.debug.tbtools.DebugTraceback", "values": [], "variance": 0}, "slots": ["_cache_all_frames", "_cache_all_tracebacks", "_te"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FOOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.tbtools.FOOTER", "name": "FOOTER", "type": "builtins.str"}}, "FRAME_HTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.tbtools.FRAME_HTML", "name": "FRAME_HTML", "type": "builtins.str"}}, "HEADER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.tbtools.HEADER", "name": "HEADER", "type": "builtins.str"}}, "PAGE_HTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.tbtools.PAGE_HTML", "name": "PAGE_HTML", "type": "builtins.str"}}, "SUMMARY_HTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.tbtools.SUMMARY_HTML", "name": "SUMMARY_HTML", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.tbtools.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.tbtools.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.tbtools.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.tbtools.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.tbtools.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.tbtools.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_process_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5], "arg_names": ["exc", "te", "skip", "hide"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools._process_traceback", "name": "_process_traceback", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["exc", "te", "skip", "hide"], "arg_types": ["builtins.BaseException", {".class": "UnionType", "items": ["traceback.TracebackException", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_traceback", "ret_type": "traceback.TracebackException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "werkzeug.utils.cached_property", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "cross_ref": "markupsafe._speedups.escape", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "linecache": {".class": "SymbolTableNode", "cross_ref": "linecache", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "render_console_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["secret", "evalex_trusted"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.tbtools.render_console_html", "name": "render_console_html", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["secret", "evalex_trusted"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_console_html", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "sysconfig": {".class": "SymbolTableNode", "cross_ref": "sysconfig", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\debug\\tbtools.py"}