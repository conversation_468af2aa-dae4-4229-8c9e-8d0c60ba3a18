{".class": "MypyFile", "_fullname": "wsproto.utilities", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACCEPT_GUID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wsproto.utilities.ACCEPT_GUID", "name": "ACCEPT_GUID", "type": "builtins.bytes"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "wsproto.events.Event", "kind": "Gdef"}, "H11Headers": {".class": "SymbolTableNode", "cross_ref": "h11._headers.Headers", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "wsproto.typing.Headers", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wsproto.utilities.ProtocolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsproto.utilities.LocalProtocolError", "name": "LocalProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsproto.utilities.LocalProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsproto.utilities", "mro": ["wsproto.utilities.LocalProtocolError", "wsproto.utilities.ProtocolError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsproto.utilities.LocalProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsproto.utilities.LocalProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsproto.utilities.ProtocolError", "name": "ProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsproto.utilities.ProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsproto.utilities", "mro": ["wsproto.utilities.ProtocolError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsproto.utilities.ProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsproto.utilities.ProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RemoteProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wsproto.utilities.ProtocolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsproto.utilities.RemoteProtocolError", "name": "RemoteProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsproto.utilities.RemoteProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsproto.utilities", "mro": ["wsproto.utilities.RemoteProtocolError", "wsproto.utilities.ProtocolError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "event_hint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.utilities.RemoteProtocolError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "event_hint"], "arg_types": ["wsproto.utilities.RemoteProtocolError", "builtins.str", {".class": "UnionType", "items": ["wsproto.events.Event", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RemoteProtocolError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event_hint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "wsproto.utilities.RemoteProtocolError.event_hint", "name": "event_hint", "type": {".class": "UnionType", "items": ["wsproto.events.Event", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsproto.utilities.RemoteProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsproto.utilities.RemoteProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.utilities.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.utilities.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.utilities.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.utilities.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.utilities.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsproto.utilities.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "generate_accept_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.utilities.generate_accept_token", "name": "generate_accept_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_accept_token", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_nonce": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.utilities.generate_nonce", "name": "generate_nonce", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_nonce", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "normed_header_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["h11_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.utilities.normed_header_dict", "name": "normed_header_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["h11_headers"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "wsproto.typing.Headers"}, "h11._headers.Headers"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normed_header_dict", "ret_type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "split_comma_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsproto.utilities.split_comma_header", "name": "split_comma_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_comma_header", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\wsproto\\utilities.py"}