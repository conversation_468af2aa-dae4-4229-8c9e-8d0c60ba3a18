{"data_mtime": **********, "dep_lines": [13, 14, 15, 16, 6, 8, 12, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 5, 5, 25, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.routing.map", "werkzeug.routing.rules", "werkzeug.wrappers.request", "werkzeug.wrappers.response", "werkzeug.exceptions", "werkzeug.utils", "_typeshed.wsgi", "__future__", "difflib", "typing", "builtins", "_frozen_importlib", "abc", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers"], "hash": "8812fa3e0bd2b0775d8719509cf4995fd9f7837b", "id": "werkzeug.routing.exceptions", "ignore_all": true, "interface_hash": "5b4f64a627a6198a3cadc2472bbd33d721469412", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\routing\\exceptions.py", "plugin_data": null, "size": 4698, "suppressed": [], "version_id": "1.15.0"}