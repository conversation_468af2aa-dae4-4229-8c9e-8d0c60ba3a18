{"data_mtime": 1753895354, "dep_lines": [12, 3, 4, 5, 6, 7, 8, 9, 10, 29, 197, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 5, 30, 30], "dependencies": ["llm_proxy_server.models", "json", "os", "shutil", "datetime", "pathlib", "typing", "structlog", "<PERSON><PERSON><PERSON>", "tempfile", "httpx", "builtins", "_frozen_importlib", "abc"], "hash": "5b06cc163ac62283c1e05c1d815e52d53f84348d", "id": "llm_proxy_server.config_manager", "ignore_all": true, "interface_hash": "56578fee111dbee5faa271c86655d8a1b55c2e76", "mtime": 1753826591, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\llm_proxy_server\\config_manager.py", "plugin_data": null, "size": 23349, "suppressed": [], "version_id": "1.15.0"}