{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives.asymmetric.ec", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BrainpoolP256R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1", "name": "BrainpoolP256R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP256R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BrainpoolP384R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1", "name": "BrainpoolP384R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP384R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BrainpoolP512R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1", "name": "BrainpoolP512R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.BrainpoolP512R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ECDH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDH", "name": "ECDH", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDH", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.ECDH", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDH.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.ECDH", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ECDSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA", "name": "ECDSA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.ECDSA", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.ECDSA", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ECDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA._algorithm", "name": "_algorithm", "type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": false}}}, "algorithm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA.algorithm", "name": "algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.ECDSA"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "algorithm of ECDSA", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA.algorithm", "name": "algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.ECDSA"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "algorithm of ECDSA", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EllipticCurve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["key_size", 1], ["name", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "name": "EllipticCurve", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of EllipticCurve", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of EllipticCurve", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of EllipticCurve", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of EllipticCurve", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EllipticCurveOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID", "name": "EllipticCurveOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID", "builtins.object"], "names": {".class": "SymbolTable", "BRAINPOOLP256R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.BRAINPOOLP256R1", "name": "BRAINPOOLP256R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "BRAINPOOLP384R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.BRAINPOOLP384R1", "name": "BRAINPOOLP384R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "BRAINPOOLP512R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.BRAINPOOLP512R1", "name": "BRAINPOOLP512R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECP192R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECP192R1", "name": "SECP192R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECP224R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECP224R1", "name": "SECP224R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECP256K1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECP256K1", "name": "SECP256K1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECP256R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECP256R1", "name": "SECP256R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECP384R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECP384R1", "name": "SECP384R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECP521R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECP521R1", "name": "SECP521R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT163K1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT163K1", "name": "SECT163K1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT163R2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT163R2", "name": "SECT163R2", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT233K1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT233K1", "name": "SECT233K1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT233R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT233R1", "name": "SECT233R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT283K1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT283K1", "name": "SECT283K1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT283R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT283R1", "name": "SECT283R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT409K1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT409K1", "name": "SECT409K1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT409R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT409R1", "name": "SECT409R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT571K1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT571K1", "name": "SECT571K1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SECT571R1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.SECT571R1", "name": "SECT571R1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EllipticCurvePrivateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["curve", 1], ["exchange", 1], ["key_size", 1], ["private_bytes", 1], ["private_numbers", 1], ["public_key", 1], ["sign", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "name": "EllipticCurvePrivateKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "builtins.object"], "names": {".class": "SymbolTable", "curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.curve", "name": "curve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "curve of EllipticCurvePrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.curve", "name": "curve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "curve of EllipticCurvePrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exchange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "algorithm", "peer_public_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.exchange", "name": "exchange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "algorithm", "peer_public_key"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.asymmetric.ec.ECDH", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exchange of EllipticCurvePrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.exchange", "name": "exchange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "algorithm", "peer_public_key"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.asymmetric.ec.ECDH", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exchange of EllipticCurvePrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of EllipticCurvePrivateKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of EllipticCurvePrivateKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.private_bytes", "name": "private_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_bytes of EllipticCurvePrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.private_bytes", "name": "private_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_bytes of EllipticCurvePrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.private_numbers", "name": "private_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_numbers of EllipticCurvePrivateKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.ec.EllipticCurvePrivateNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.private_numbers", "name": "private_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_numbers of EllipticCurvePrivateKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.ec.EllipticCurvePrivateNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of EllipticCurvePrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of EllipticCurvePrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "signature_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "signature_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of EllipticCurvePrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "signature_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of EllipticCurvePrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EllipticCurvePrivateKeyWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKeyWithSerialization", "line": 124, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"}}, "EllipticCurvePrivateNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateNumbers", "line": 194, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.ec.EllipticCurvePrivateNumbers"}}, "EllipticCurvePublicKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__eq__", 1], ["curve", 1], ["key_size", 1], ["public_bytes", 1], ["public_numbers", 1], ["verify", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "name": "EllipticCurvePublicKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of EllipticCurvePublicKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of EllipticCurvePublicKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.curve", "name": "curve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "curve of EllipticCurvePublicKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.curve", "name": "curve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "curve of EllipticCurvePublicKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_encoded_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "curve", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.from_encoded_point", "name": "from_encoded_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "curve", "data"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"}, "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_encoded_point of EllipticCurvePublicKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.from_encoded_point", "name": "from_encoded_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "curve", "data"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"}, "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_encoded_point of EllipticCurvePublicKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of EllipticCurvePublicKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of EllipticCurvePublicKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PublicFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of EllipticCurvePublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PublicFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of EllipticCurvePublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of EllipticCurvePublicKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.ec.EllipticCurvePublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of EllipticCurvePublicKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.ec.EllipticCurvePublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signature", "data", "signature_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signature", "data", "signature_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "builtins.bytes", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of EllipticCurvePublicKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signature", "data", "signature_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "builtins.bytes", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of EllipticCurvePublicKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EllipticCurvePublicKeyWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKeyWithSerialization", "line": 191, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"}}, "EllipticCurvePublicNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicNumbers", "line": 195, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.ec.EllipticCurvePublicNumbers"}}, "EllipticCurveSignatureAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["algorithm", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm", "name": "EllipticCurveSignatureAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm", "builtins.object"], "names": {".class": "SymbolTable", "algorithm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm.algorithm", "name": "algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "algorithm of EllipticCurveSignatureAlgorithm", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm.algorithm", "name": "algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "algorithm of EllipticCurveSignatureAlgorithm", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurveSignatureAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObjectIdentifier": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.ObjectIdentifier", "kind": "Gdef"}, "SECP192R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP192R1", "name": "SECP192R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP192R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECP192R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP192R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP192R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP192R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECP192R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECP224R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP224R1", "name": "SECP224R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP224R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECP224R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP224R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP224R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP224R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECP224R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECP256K1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1", "name": "SECP256K1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECP256K1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECP256R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1", "name": "SECP256R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECP256R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECP384R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1", "name": "SECP384R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECP384R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECP521R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1", "name": "SECP521R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECP521R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT163K1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163K1", "name": "SECT163K1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163K1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT163K1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163K1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163K1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163K1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT163K1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT163R2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163R2", "name": "SECT163R2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163R2", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT163R2", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163R2.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163R2.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT163R2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT163R2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT233K1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233K1", "name": "SECT233K1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233K1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT233K1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233K1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233K1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233K1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT233K1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT233R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233R1", "name": "SECT233R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT233R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT233R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT233R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT283K1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283K1", "name": "SECT283K1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283K1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT283K1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283K1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283K1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283K1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT283K1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT283R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283R1", "name": "SECT283R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT283R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT283R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT283R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT409K1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409K1", "name": "SECT409K1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409K1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT409K1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409K1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409K1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409K1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT409K1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT409R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409R1", "name": "SECT409R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT409R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT409R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT409R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT571K1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571K1", "name": "SECT571K1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571K1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT571K1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571K1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571K1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571K1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT571K1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SECT571R1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571R1", "name": "SECT571R1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571R1", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.ec", "mro": ["cryptography.hazmat.primitives.asymmetric.ec.SECT571R1", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "builtins.object"], "names": {".class": "SymbolTable", "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571R1.key_size", "name": "key_size", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571R1.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.ec.SECT571R1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.ec.SECT571R1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CURVE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec._CURVE_TYPES", "name": "_CURVE_TYPES", "type": {".class": "Instance", "args": ["builtins.str", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_OID_TO_CURVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec._OID_TO_CURVE", "name": "_OID_TO_CURVE", "type": {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["cryptography.hazmat.primitives.asymmetric.ec.SECT571R1"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_serialization": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asym_utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.utils", "kind": "Gdef"}, "derive_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["private_value", "curve", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.derive_private_key", "name": "derive_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["private_value", "curve", "backend"], "arg_types": ["builtins.int", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "derive_private_key", "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.generate_private_key", "name": "generate_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["curve", "backend"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_curve_for_oid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["oid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.ec.get_curve_for_oid", "name": "get_curve_for_oid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["oid"], "arg_types": ["cryptography.hazmat.bindings._rust.ObjectIdentifier"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_curve_for_oid", "ret_type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "rust_openssl": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.utils", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py"}