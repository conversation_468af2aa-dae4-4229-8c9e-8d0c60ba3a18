{"data_mtime": 1753889303, "dep_lines": [9, 12, 9, 5, 7, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 20, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.exceptions", "cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.bindings._rust", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "cryptography.hazmat", "cryptography.hazmat.bindings"], "hash": "e77dfc340a0d22d654f97218bf1dd72820b585fc", "id": "cryptography.exceptions", "ignore_all": true, "interface_hash": "a2a13f9b7db4e6d579d7a1f4d45d5b09d2102249", "mtime": 1708667824, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\exceptions.py", "plugin_data": null, "size": 1087, "suppressed": [], "version_id": "1.15.0"}