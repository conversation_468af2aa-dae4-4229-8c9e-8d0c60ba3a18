{".class": "MypyFile", "_fullname": "uvicorn.main", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIApplication": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIApplication", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChangeReload": {".class": "SymbolTableNode", "cross_ref": "uvicorn.supervisors.ChangeReload", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.Config", "kind": "Gdef"}, "HTTPProtocolType": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.HTTPProtocolType", "kind": "Gdef"}, "HTTP_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.HTTP_CHOICES", "name": "HTTP_CHOICES", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "h11"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "httptools"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "click.types.Choice"}}}, "HTTP_PROTOCOLS": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.HTTP_PROTOCOLS", "kind": "Gdef"}, "INTERFACES": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.INTERFACES", "kind": "Gdef"}, "INTERFACE_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.INTERFACE_CHOICES", "name": "INTERFACE_CHOICES", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "asgi3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "asgi2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wsgi"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "click.types.Choice"}}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "InterfaceType": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.InterfaceType", "kind": "Gdef"}, "LEVEL_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.LEVEL_CHOICES", "name": "LEVEL_CHOICES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "click.types.Choice"}}}, "LIFESPAN": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.LIFESPAN", "kind": "Gdef"}, "LIFESPAN_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.LIFESPAN_CHOICES", "name": "LIFESPAN_CHOICES", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "on"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "off"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "click.types.Choice"}}}, "LOGGING_CONFIG": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.LOGGING_CONFIG", "kind": "Gdef"}, "LOG_LEVELS": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.LOG_LEVELS", "kind": "Gdef"}, "LOOP_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.LOOP_CHOICES", "name": "LOOP_CHOICES", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "asyncio"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uvloop"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "click.types.Choice"}}}, "LOOP_SETUPS": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.LOOP_SETUPS", "kind": "Gdef"}, "LifespanType": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.LifespanType", "kind": "Gdef"}, "LoopSetupType": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.LoopSetupType", "kind": "Gdef"}, "Multiprocess": {".class": "SymbolTableNode", "cross_ref": "uvicorn.supervisors.multiprocess.Multiprocess", "kind": "Gdef"}, "RawConfigParser": {".class": "SymbolTableNode", "cross_ref": "configparser.RawConfigParser", "kind": "Gdef"}, "SSL_PROTOCOL_VERSION": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.SSL_PROTOCOL_VERSION", "kind": "Gdef"}, "STARTUP_FAILURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.STARTUP_FAILURE", "name": "STARTUP_FAILURE", "type": "builtins.int"}}, "Server": {".class": "SymbolTableNode", "cross_ref": "uvicorn.server.Server", "kind": "Gdef"}, "WSProtocolType": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.WSProtocolType", "kind": "Gdef"}, "WS_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.WS_CHOICES", "name": "WS_CHOICES", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "websockets"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wsproto"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "click.types.Choice"}}}, "WS_PROTOCOLS": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.WS_PROTOCOLS", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.main.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.main.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.main.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.main.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.main.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.main.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.main.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "click": {".class": "SymbolTableNode", "cross_ref": "click", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "uvicorn.main.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["app", "host", "port", "uds", "fd", "loop", "http", "ws", "ws_max_size", "ws_max_queue", "ws_ping_interval", "ws_ping_timeout", "ws_per_message_deflate", "lifespan", "interface", "reload", "reload_dirs", "reload_includes", "reload_excludes", "reload_delay", "workers", "env_file", "log_config", "log_level", "access_log", "proxy_headers", "server_header", "date_header", "forwarded_allow_ips", "root_path", "limit_concurrency", "backlog", "limit_max_requests", "timeout_keep_alive", "timeout_graceful_shutdown", "ssl_keyfile", "ssl_certfile", "ssl_keyfile_password", "ssl_version", "ssl_cert_reqs", "ssl_ca_certs", "ssl_ciphers", "headers", "use_colors", "app_dir", "h11_max_incomplete_event_size", "factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "uvicorn.main.main", "name": "main", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["app", "host", "port", "uds", "fd", "loop", "http", "ws", "ws_max_size", "ws_max_queue", "ws_ping_interval", "ws_ping_timeout", "ws_per_message_deflate", "lifespan", "interface", "reload", "reload_dirs", "reload_includes", "reload_excludes", "reload_delay", "workers", "env_file", "log_config", "log_level", "access_log", "proxy_headers", "server_header", "date_header", "forwarded_allow_ips", "root_path", "limit_concurrency", "backlog", "limit_max_requests", "timeout_keep_alive", "timeout_graceful_shutdown", "ssl_keyfile", "ssl_certfile", "ssl_keyfile_password", "ssl_version", "ssl_cert_reqs", "ssl_ca_certs", "ssl_ciphers", "headers", "use_colors", "app_dir", "h11_max_incomplete_event_size", "factory"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", "builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.LoopSetupType"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.HTTPProtocolType"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.WSProtocolType"}, "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.LifespanType"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.InterfaceType"}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "main", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "uvicorn.main.main", "name": "main", "type": "click.core.Command"}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "print_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.main.print_version", "name": "print_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "param", "value"], "arg_types": ["click.core.Context", "click.core.Parameter", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_version", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["app", "host", "port", "uds", "fd", "loop", "http", "ws", "ws_max_size", "ws_max_queue", "ws_ping_interval", "ws_ping_timeout", "ws_per_message_deflate", "lifespan", "interface", "reload", "reload_dirs", "reload_includes", "reload_excludes", "reload_delay", "workers", "env_file", "log_config", "log_level", "access_log", "proxy_headers", "server_header", "date_header", "forwarded_allow_ips", "root_path", "limit_concurrency", "backlog", "limit_max_requests", "timeout_keep_alive", "timeout_graceful_shutdown", "ssl_keyfile", "ssl_certfile", "ssl_keyfile_password", "ssl_version", "ssl_cert_reqs", "ssl_ca_certs", "ssl_ciphers", "headers", "use_colors", "app_dir", "factory", "h11_max_incomplete_event_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "uvicorn.main.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["app", "host", "port", "uds", "fd", "loop", "http", "ws", "ws_max_size", "ws_max_queue", "ws_ping_interval", "ws_ping_timeout", "ws_per_message_deflate", "lifespan", "interface", "reload", "reload_dirs", "reload_includes", "reload_excludes", "reload_delay", "workers", "env_file", "log_config", "log_level", "access_log", "proxy_headers", "server_header", "date_header", "forwarded_allow_ips", "root_path", "limit_concurrency", "backlog", "limit_max_requests", "timeout_keep_alive", "timeout_graceful_shutdown", "ssl_keyfile", "ssl_certfile", "ssl_keyfile_password", "ssl_version", "ssl_cert_reqs", "ssl_ca_certs", "ssl_ciphers", "headers", "use_colors", "app_dir", "factory", "h11_max_incomplete_event_size"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIApplication"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.LoopSetupType"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "asyncio.protocols.Protocol"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.HTTPProtocolType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "asyncio.protocols.Protocol"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.WSProtocolType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.LifespanType"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.config.InterfaceType"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "configparser.RawConfigParser", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "uvicorn": {".class": "SymbolTableNode", "cross_ref": "u<PERSON><PERSON>", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\uvicorn\\main.py"}