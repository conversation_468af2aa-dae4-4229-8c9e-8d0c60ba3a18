{".class": "MypyFile", "_fullname": "websockets.legacy.framing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BytesLike": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.BytesLike", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Close": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Close", "kind": "Gdef"}, "Data": {".class": "SymbolTableNode", "cross_ref": "websockets.typing.Data", "kind": "Gdef"}, "Frame": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "websockets.legacy.framing.Frame", "name": "<PERSON>ame", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "websockets.legacy.framing.Frame", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["fin", "opcode", "data", "rsv1", "rsv2", "rsv3"]}}, "module_name": "websockets.legacy.framing", "mro": ["websockets.legacy.framing.Frame", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.legacy.framing.Frame.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.legacy.framing.Frame.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["_cls", "fin", "opcode", "data", "rsv1", "rsv2", "rsv3"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "websockets.legacy.framing.Frame.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["_cls", "fin", "opcode", "data", "rsv1", "rsv2", "rsv3"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Frame", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.Frame.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Frame", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.Frame._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of Frame", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.legacy.framing.Frame._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.legacy.framing.Frame._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.legacy.framing.Frame._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "websockets.legacy.framing.Frame._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Frame", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "websockets.legacy.framing.Frame._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Frame", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "fin", "opcode", "data", "rsv1", "rsv2", "rsv3"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.Frame._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "fin", "opcode", "data", "rsv1", "rsv2", "rsv3"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of Frame", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame._NT", "id": -1, "name": "_NT", "namespace": "websockets.legacy.framing.Frame._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "websockets.legacy.framing.Frame._source", "name": "_source", "type": "builtins.str"}}, "check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.Frame.check", "name": "check", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "websockets.legacy.framing.Frame.data", "name": "data", "type": "builtins.bytes"}}, "data-redefinition": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.framing.Frame.data", "kind": "<PERSON><PERSON><PERSON>"}, "fin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "websockets.legacy.framing.Frame.fin", "name": "fin", "type": "builtins.bool"}}, "fin-redefinition": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.framing.Frame.fin", "kind": "<PERSON><PERSON><PERSON>"}, "new_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "websockets.legacy.framing.Frame.new_frame", "name": "new_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_frame of Frame", "ret_type": "websockets.frames.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "websockets.legacy.framing.Frame.new_frame", "name": "new_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_frame of Frame", "ret_type": "websockets.frames.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "opcode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "websockets.legacy.framing.Frame.opcode", "name": "opcode", "type": "websockets.frames.Opcode"}}, "opcode-redefinition": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.framing.Frame.opcode", "kind": "<PERSON><PERSON><PERSON>"}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5], "arg_names": ["cls", "reader", "mask", "max_size", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "websockets.legacy.framing.Frame.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5], "arg_names": ["cls", "reader", "mask", "max_size", "extensions"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "websockets.legacy.framing.Frame"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "websockets.legacy.framing.Frame.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5], "arg_names": ["cls", "reader", "mask", "max_size", "extensions"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "websockets.legacy.framing.Frame"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rsv1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "websockets.legacy.framing.Frame.rsv1", "name": "rsv1", "type": "builtins.bool"}}, "rsv1-redefinition": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.framing.Frame.rsv1", "kind": "<PERSON><PERSON><PERSON>"}, "rsv2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "websockets.legacy.framing.Frame.rsv2", "name": "rsv2", "type": "builtins.bool"}}, "rsv2-redefinition": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.framing.Frame.rsv2", "kind": "<PERSON><PERSON><PERSON>"}, "rsv3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "websockets.legacy.framing.Frame.rsv3", "name": "rsv3", "type": "builtins.bool"}}, "rsv3-redefinition": {".class": "SymbolTableNode", "cross_ref": "websockets.legacy.framing.Frame.rsv3", "kind": "<PERSON><PERSON><PERSON>"}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "write", "mask", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.Frame.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "write", "mask", "extensions"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["websockets.extensions.base.Extension"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "websockets.legacy.framing.Frame.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": "websockets.legacy.framing.Frame"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bool", "websockets.frames.Opcode", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "PayloadTooBig": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.PayloadTooBig", "kind": "Gdef"}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.ProtocolError", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.framing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.framing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.framing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.framing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.framing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "websockets.legacy.framing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "apply_mask": {".class": "SymbolTableNode", "cross_ref": "websockets.speedups.apply_mask", "kind": "Gdef"}, "encode_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "websockets.legacy.framing.encode_data", "name": "encode_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.Data"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extensions": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions", "kind": "Gdef"}, "frames": {".class": "SymbolTableNode", "cross_ref": "websockets.frames", "kind": "Gdef"}, "parse_close": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.parse_close", "name": "parse_close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_close", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_ctrl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.prepare_ctrl", "name": "prepare_ctrl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.Data"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_ctrl", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.prepare_data", "name": "prepare_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "websockets.typing.Data"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_data", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serialize_close": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["code", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "websockets.legacy.framing.serialize_close", "name": "serialize_close", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["code", "reason"], "arg_types": ["builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_close", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\websockets\\legacy\\framing.py"}