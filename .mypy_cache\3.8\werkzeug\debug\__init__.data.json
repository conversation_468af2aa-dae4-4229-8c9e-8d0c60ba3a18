{".class": "MypyFile", "_fullname": "werkzeug.debug", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "cross_ref": "werkzeug.debug.console.Console", "kind": "Gdef"}, "DebugFrameSummary": {".class": "SymbolTableNode", "cross_ref": "werkzeug.debug.tbtools.DebugFrameSummary", "kind": "Gdef"}, "DebugTraceback": {".class": "SymbolTableNode", "cross_ref": "werkzeug.debug.tbtools.DebugTraceback", "kind": "Gdef"}, "DebuggedApplication": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.debug.DebuggedApplication", "name": "DebuggedApplication", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.debug", "mro": ["werkzeug.debug.DebuggedApplication", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "environ", "start_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "environ", "start_response"], "arg_types": ["werkzeug.debug.DebuggedApplication", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "_typeshed.wsgi.StartResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of DebuggedApplication", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "evalex", "request_key", "console_path", "console_init_func", "show_hidden_frames", "pin_security", "pin_logging"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "evalex", "request_key", "console_path", "console_init_func", "show_hidden_frames", "pin_security", "pin_logging"], "arg_types": ["werkzeug.debug.DebuggedApplication", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, "builtins.bool", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DebuggedApplication", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fail_pin_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication._fail_pin_auth", "name": "_fail_pin_auth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fail_pin_auth of DebuggedApplication", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_failed_pin_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication._failed_pin_auth", "name": "_failed_pin_auth", "type": "builtins.int"}}, "_pin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "werkzeug.debug.DebuggedApplication._pin", "name": "_pin", "type": "builtins.str"}}, "_pin_cookie": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "werkzeug.debug.DebuggedApplication._pin_cookie", "name": "_pin_cookie", "type": "builtins.str"}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "_typeshed.wsgi.StartResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_pin_trust": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "environ"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.check_pin_trust", "name": "check_pin_trust", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "environ"], "arg_types": ["werkzeug.debug.DebuggedApplication", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_pin_trust of DebuggedApplication", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "console_init_func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.console_init_func", "name": "console_init_func", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "console_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.console_path", "name": "console_path", "type": "builtins.str"}}, "debug_application": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "environ", "start_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.debug_application", "name": "debug_application", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "environ", "start_response"], "arg_types": ["werkzeug.debug.DebuggedApplication", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "_typeshed.wsgi.StartResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_application of DebuggedApplication", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_console": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.display_console", "name": "display_console", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["werkzeug.debug.DebuggedApplication", "werkzeug.wrappers.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "display_console of DebuggedApplication", "ret_type": "werkzeug.wrappers.response.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evalex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.evalex", "name": "evalex", "type": "builtins.bool"}}, "execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "command", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.execute_command", "name": "execute_command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "command", "frame"], "arg_types": ["werkzeug.debug.DebuggedApplication", "werkzeug.wrappers.request.Request", "builtins.str", {".class": "UnionType", "items": ["werkzeug.debug.tbtools.DebugFrameSummary", "werkzeug.debug._ConsoleFrame"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_command of DebuggedApplication", "ret_type": "werkzeug.wrappers.response.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frame_contexts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.frame_contexts", "name": "frame_contexts", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.ContextManager"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "frames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.frames", "name": "frames", "type": {".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": ["werkzeug.debug.tbtools.DebugFrameSummary", "werkzeug.debug._ConsoleFrame"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.get_resource", "name": "get_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "filename"], "arg_types": ["werkzeug.debug.DebuggedApplication", "werkzeug.wrappers.request.Request", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource of DebuggedApplication", "ret_type": "werkzeug.wrappers.response.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log_pin_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.log_pin_request", "name": "log_pin_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_pin_request of DebuggedApplication", "ret_type": "werkzeug.wrappers.response.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "werkzeug.debug.DebuggedApplication.pin", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "werkzeug.debug.DebuggedApplication.pin", "name": "pin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin of DebuggedApplication", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.pin", "name": "pin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin of DebuggedApplication", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "werkzeug.debug.DebuggedApplication.pin", "name": "pin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["werkzeug.debug.DebuggedApplication", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin of DebuggedApplication", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "pin", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin of DebuggedApplication", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "pin_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.DebuggedApplication.pin_auth", "name": "pin_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["werkzeug.debug.DebuggedApplication", "werkzeug.wrappers.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin_auth of DebuggedApplication", "ret_type": "werkzeug.wrappers.response.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pin_cookie_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "werkzeug.debug.DebuggedApplication.pin_cookie_name", "name": "pin_cookie_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin_cookie_name of DebuggedApplication", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.pin_cookie_name", "name": "pin_cookie_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["werkzeug.debug.DebuggedApplication"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pin_cookie_name of DebuggedApplication", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pin_logging": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.pin_logging", "name": "pin_logging", "type": "builtins.bool"}}, "request_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.request_key", "name": "request_key", "type": "builtins.str"}}, "secret": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.secret", "name": "secret", "type": "builtins.str"}}, "show_hidden_frames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug.DebuggedApplication.show_hidden_frames", "name": "show_hidden_frames", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.debug.DebuggedApplication.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.debug.DebuggedApplication", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExitStack": {".class": "SymbolTableNode", "cross_ref": "contextlib.ExitStack", "kind": "Gdef"}, "NotFound": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.NotFound", "kind": "Gdef"}, "PIN_TIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.debug.PIN_TIME", "name": "PIN_TIME", "type": "builtins.int"}}, "Request": {".class": "SymbolTableNode", "cross_ref": "werkzeug.wrappers.request.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "werkzeug.wrappers.response.Response", "kind": "Gdef"}, "StartResponse": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.StartResponse", "kind": "Gdef"}, "WSGIApplication": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIApplication", "kind": "Gdef"}, "WSGIEnvironment": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIEnvironment", "kind": "Gdef"}, "_ConsoleFrame": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.debug._ConsoleFrame", "name": "_ConsoleFrame", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.debug._ConsoleFrame", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.debug", "mro": ["werkzeug.debug._ConsoleFrame", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug._ConsoleFrame.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "namespace"], "arg_types": ["werkzeug.debug._ConsoleFrame", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ConsoleFrame", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug._ConsoleFrame.console", "name": "console", "type": "werkzeug.debug.console.Console"}}, "eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug._ConsoleFrame.eval", "name": "eval", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["werkzeug.debug._ConsoleFrame", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eval of _ConsoleFrame", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.debug._ConsoleFrame.id", "name": "id", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.debug._ConsoleFrame.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.debug._ConsoleFrame", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.debug.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_log": {".class": "SymbolTableNode", "cross_ref": "werkzeug._internal._log", "kind": "Gdef"}, "_machine_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "werkzeug.debug._machine_id", "name": "_machine_id", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "adler32": {".class": "SymbolTableNode", "cross_ref": "zlib.adler32", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "basename": {".class": "SymbolTableNode", "cross_ref": "posixpath.basename", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "gen_salt": {".class": "SymbolTableNode", "cross_ref": "werkzeug.security.gen_salt", "kind": "Gdef"}, "get_machine_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.get_machine_id", "name": "get_machine_id", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_machine_id", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_pin_and_cookie_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.get_pin_and_cookie_name", "name": "get_pin_and_cookie_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["app"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pin_and_cookie_name", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpass": {".class": "SymbolTableNode", "cross_ref": "getpass", "kind": "Gdef"}, "hash_pin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.debug.hash_pin", "name": "hash_pin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pin"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hash_pin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "ntpath.join", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_cookie": {".class": "SymbolTableNode", "cross_ref": "werkzeug.http.parse_cookie", "kind": "Gdef"}, "pkgutil": {".class": "SymbolTableNode", "cross_ref": "pkgu<PERSON>", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "render_console_html": {".class": "SymbolTableNode", "cross_ref": "werkzeug.debug.tbtools.render_console_html", "kind": "Gdef"}, "send_file": {".class": "SymbolTableNode", "cross_ref": "werkzeug.utils.send_file", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\debug\\__init__.py"}