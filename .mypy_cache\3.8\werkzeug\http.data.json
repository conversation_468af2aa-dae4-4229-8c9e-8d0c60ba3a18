{".class": "MypyFile", "_fullname": "werkzeug.http", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "COEP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.http.COEP", "name": "COEP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "werkzeug.http.COEP", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "werkzeug.http", "mro": ["werkzeug.http.COEP", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "REQUIRE_CORP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.http.COEP.REQUIRE_CORP", "name": "REQUIRE_CORP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "require-corp"}, "type_ref": "builtins.str"}}}, "UNSAFE_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.http.COEP.UNSAFE_NONE", "name": "UNSAFE_NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsafe-none"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http.COEP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.http.COEP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "COOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.http.COOP", "name": "COOP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "werkzeug.http.COOP", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "werkzeug.http", "mro": ["werkzeug.http.COOP", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "SAME_ORIGIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.http.COOP.SAME_ORIGIN", "name": "SAME_ORIGIN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "same-origin"}, "type_ref": "builtins.str"}}}, "SAME_ORIGIN_ALLOW_POPUPS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.http.COOP.SAME_ORIGIN_ALLOW_POPUPS", "name": "SAME_ORIGIN_ALLOW_POPUPS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "same-origin-allow-popups"}, "type_ref": "builtins.str"}}}, "UNSAFE_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "werkzeug.http.COOP.UNSAFE_NONE", "name": "UNSAFE_NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsafe-none"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http.COOP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.http.COOP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "HTTP_STATUS_CODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http.HTTP_STATUS_CODES", "name": "HTTP_STATUS_CODES", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "WSGIEnvironment": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIEnvironment", "kind": "Gdef"}, "_TAnyAccept": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "name": "_TAnyAccept", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}}, "_TAnyCC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "name": "_TAnyCC", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}}, "_TAnyCSP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "name": "_TAnyCSP", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.http.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.http.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.http.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.http.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.http.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.http.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_charset_value_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._charset_value_re", "name": "_charset_value_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_continuation_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._continuation_re", "name": "_continuation_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_cookie_no_quote_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._cookie_no_quote_re", "name": "_cookie_no_quote_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_cookie_slash_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._cookie_slash_map", "name": "_cookie_slash_map", "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_cookie_slash_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._cookie_slash_re", "name": "_cookie_slash_re", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_dt_as_utc": {".class": "SymbolTableNode", "cross_ref": "werkzeug._internal._dt_as_utc", "kind": "Gdef"}, "_entity_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._entity_headers", "name": "_entity_headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_etag_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._etag_re", "name": "_etag_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_hop_by_hop_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._hop_by_hop_headers", "name": "_hop_by_hop_headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_parameter_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._parameter_re", "name": "_parameter_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_parse_list_header": {".class": "SymbolTableNode", "cross_ref": "urllib.request.parse_http_list", "kind": "Gdef"}, "_plain_int": {".class": "SymbolTableNode", "cross_ref": "werkzeug._internal._plain_int", "kind": "Gdef"}, "_q_value_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._q_value_re", "name": "_q_value_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_sansio_http": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.http", "kind": "Gdef"}, "_t_cc_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": 1, "name": "_TAnyCC", "namespace": "werkzeug.http._t_cc_update", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}], "column": 0, "fullname": "werkzeug.http._t_cc_update", "line": 619, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": 1, "name": "_TAnyCC", "namespace": "werkzeug.http._t_cc_update", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_t_csp_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": 1, "name": "_TAnyCSP", "namespace": "werkzeug.http._t_csp_update", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}], "column": 0, "fullname": "werkzeug.http._t_csp_update", "line": 667, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": 1, "name": "_TAnyCSP", "namespace": "werkzeug.http._t_csp_update", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_token_chars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "werkzeug.http._token_chars", "name": "_token_chars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "ds": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures", "kind": "Gdef"}, "dump_age": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["age"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.dump_age", "name": "dump_age", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["age"], "arg_types": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_age", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_cookie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["key", "value", "max_age", "expires", "path", "domain", "secure", "httponly", "sync_expires", "max_size", "samesite"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.dump_cookie", "name": "dump_cookie", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["key", "value", "max_age", "expires", "path", "domain", "secure", "httponly", "sync_expires", "max_size", "samesite"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "datetime.datetime", "builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_cookie", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_csp_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.dump_csp_header", "name": "dump_csp_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["werkzeug.datastructures.csp.ContentSecurityPolicy"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_csp_header", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.dump_header", "name": "dump_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iterable"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_header", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_options_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["header", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.dump_options_header", "name": "dump_options_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["header", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_options_header", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "email": {".class": "SymbolTableNode", "cross_ref": "email", "kind": "Gdef"}, "generate_etag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.generate_etag", "name": "generate_etag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_etag", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "http_date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.http_date", "name": "http_date", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["timestamp"], "arg_types": [{".class": "UnionType", "items": ["datetime.datetime", "datetime.date", "builtins.int", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "time.struct_time"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "http_date", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_byte_range_valid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["start", "stop", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.is_byte_range_valid", "name": "is_byte_range_valid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["start", "stop", "length"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_byte_range_valid", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_entity_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.is_entity_header", "name": "is_entity_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_entity_header", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_hop_by_hop_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.is_hop_by_hop_header", "name": "is_hop_by_hop_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_hop_by_hop_header", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_resource_modified": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["environ", "etag", "data", "last_modified", "ignore_if_range"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.is_resource_modified", "name": "is_resource_modified", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["environ", "etag", "data", "last_modified", "ignore_if_range"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_resource_modified", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mktime": {".class": "SymbolTableNode", "cross_ref": "time.mktime", "kind": "Gdef"}, "parse_accept_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_accept_header", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "werkzeug.http.parse_accept_header", "name": "parse_accept_header", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.http.parse_accept_header", "name": "parse_accept_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": "werkzeug.datastructures.accept.Accept", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.http.parse_accept_header", "name": "parse_accept_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": "werkzeug.datastructures.accept.Accept", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.http.parse_accept_header", "name": "parse_accept_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.http.parse_accept_header", "name": "parse_accept_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": "werkzeug.datastructures.accept.Accept", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_accept_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyAccept", "id": -1, "name": "_TAnyAccept", "namespace": "werkzeug.http.parse_accept_header#1", "upper_bound": "werkzeug.datastructures.accept.Accept", "values": [], "variance": 0}]}]}}}, "parse_age": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_age", "name": "parse_age", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_age", "ret_type": {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_cache_control_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_cache_control_header", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["value", "on_update", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "werkzeug.http.parse_cache_control_header", "name": "parse_cache_control_header", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.http.parse_cache_control_header", "name": "parse_cache_control_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": "werkzeug.datastructures.cache_control.RequestCacheControl", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.http.parse_cache_control_header", "name": "parse_cache_control_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": "werkzeug.datastructures.cache_control.RequestCacheControl", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.http.parse_cache_control_header", "name": "parse_cache_control_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.http.parse_cache_control_header", "name": "parse_cache_control_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": "werkzeug.datastructures.cache_control.RequestCacheControl", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_cc_update"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cache_control_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCC", "id": -1, "name": "_TAnyCC", "namespace": "werkzeug.http.parse_cache_control_header#1", "upper_bound": "werkzeug.datastructures.cache_control._CacheControl", "values": [], "variance": 0}]}]}}}, "parse_content_range_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "on_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_content_range_header", "name": "parse_content_range_header", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "on_update"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["werkzeug.datastructures.range.ContentRange"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_content_range_header", "ret_type": {".class": "UnionType", "items": ["werkzeug.datastructures.range.ContentRange", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_cookie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["header", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_cookie", "name": "parse_cookie", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["header", "cls"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_cookie", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_csp_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_csp_header", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["value", "on_update", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "werkzeug.http.parse_csp_header", "name": "parse_csp_header", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.http.parse_csp_header", "name": "parse_csp_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": "werkzeug.datastructures.csp.ContentSecurityPolicy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.http.parse_csp_header", "name": "parse_csp_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": "werkzeug.datastructures.csp.ContentSecurityPolicy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "werkzeug.http.parse_csp_header", "name": "parse_csp_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "werkzeug.http.parse_csp_header", "name": "parse_csp_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": "werkzeug.datastructures.csp.ContentSecurityPolicy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "on_update", "cls"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "werkzeug.http._t_csp_update"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_csp_header", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.http._TAnyCSP", "id": -1, "name": "_TAnyCSP", "namespace": "werkzeug.http.parse_csp_header#1", "upper_bound": "werkzeug.datastructures.csp.ContentSecurityPolicy", "values": [], "variance": 0}]}]}}}, "parse_date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_date", "name": "parse_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_date", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_dict_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_dict_header", "name": "parse_dict_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_dict_header", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_etags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_etags", "name": "parse_etags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_etags", "ret_type": "werkzeug.datastructures.etag.ETags", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_if_range_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_if_range_header", "name": "parse_if_range_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_if_range_header", "ret_type": "werkzeug.datastructures.range.IfRange", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_list_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_list_header", "name": "parse_list_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_list_header", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_options_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_options_header", "name": "parse_options_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_options_header", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_range_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "make_inclusive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_range_header", "name": "parse_range_header", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "make_inclusive"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_range_header", "ret_type": {".class": "UnionType", "items": ["werkzeug.datastructures.range.Range", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_set_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "on_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.parse_set_header", "name": "parse_set_header", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "on_update"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["werkzeug.datastructures.structures.HeaderSet"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_set_header", "ret_type": "werkzeug.datastructures.structures.HeaderSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "quote_etag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["etag", "weak"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.quote_etag", "name": "quote_etag", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["etag", "weak"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_etag", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quote_header_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "allow_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.quote_header_value", "name": "quote_header_value", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "allow_token"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_header_value", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "remove_entity_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["headers", "allowed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.remove_entity_headers", "name": "remove_entity_headers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["headers", "allowed"], "arg_types": [{".class": "UnionType", "items": ["werkzeug.datastructures.headers.Headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_entity_headers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_hop_by_hop_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.remove_hop_by_hop_headers", "name": "remove_hop_by_hop_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": [{".class": "UnionType", "items": ["werkzeug.datastructures.headers.Headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_hop_by_hop_headers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sha1": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha1", "kind": "Gdef"}, "struct_time": {".class": "SymbolTableNode", "cross_ref": "time.struct_time", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "unquote_etag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["etag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.unquote_etag", "name": "unquote_etag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["etag"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unquote_etag", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unquote_header_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.http.unquote_header_value", "name": "unquote_header_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unquote_header_value", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\http.py"}