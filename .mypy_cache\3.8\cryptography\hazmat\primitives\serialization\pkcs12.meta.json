{"data_mtime": 1753889303, "dep_lines": [12, 12, 12, 12, 12, 19, 10, 11, 12, 10, 9, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 10, 5, 20, 20, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.dsa", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.ed448", "cryptography.hazmat.primitives.asymmetric.ed25519", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.asymmetric.types", "cryptography.hazmat.primitives.serialization", "cryptography.hazmat.primitives._serialization", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives", "cryptography.x509", "__future__", "typing", "cryptography", "builtins", "_frozen_importlib", "abc", "cryptography.hazmat.primitives.asymmetric.dh", "cryptography.hazmat.primitives.asymmetric.x25519", "cryptography.hazmat.primitives.asymmetric.x448", "cryptography.x509.base", "typing_extensions"], "hash": "5919f7af21a80db9215318065ff3d2bb605c11d5", "id": "cryptography.hazmat.primitives.serialization.pkcs12", "ignore_all": true, "interface_hash": "982f073c1577ba244cefd4add40a33e1ace6fa0c", "mtime": 1708667824, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py", "plugin_data": null, "size": 6599, "suppressed": [], "version_id": "1.15.0"}