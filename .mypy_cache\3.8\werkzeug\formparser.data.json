{".class": "MypyFile", "_fullname": "werkzeug.formparser", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "Data": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.multipart.Data", "kind": "Gdef"}, "Epilogue": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.multipart.Epilogue", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.formparser.F", "name": "F", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "Field": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.multipart.Field", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.multipart.File", "kind": "Gdef"}, "FileStorage": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.file_storage.FileStorage", "kind": "Gdef"}, "FormDataParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.formparser.FormDataParser", "name": "FormData<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.FormDataParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.formparser", "mro": ["werkzeug.formparser.FormDataParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "stream_factory", "max_form_memory_size", "max_content_length", "cls", "silent", "max_form_parts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.FormDataParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 5], "arg_names": ["self", "stream_factory", "max_form_memory_size", "max_content_length", "cls", "silent", "max_form_parts"], "arg_types": ["werkzeug.formparser.FormDataParser", {".class": "UnionType", "items": ["werkzeug.formparser.TStreamFactory", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormDataParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "stream", "mimetype", "content_length", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.FormDataParser._parse_multipart", "name": "_parse_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "stream", "mimetype", "content_length", "options"], "arg_types": ["werkzeug.formparser.FormDataParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_multipart of FormDataParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "werkzeug.formparser.t_parse_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_urlencoded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "stream", "mimetype", "content_length", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.FormDataParser._parse_urlencoded", "name": "_parse_urlencoded", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "stream", "mimetype", "content_length", "options"], "arg_types": ["werkzeug.formparser.FormDataParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_urlencoded of FormDataParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "werkzeug.formparser.t_parse_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.FormDataParser.cls", "name": "cls", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}}}}, "max_content_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.FormDataParser.max_content_length", "name": "max_content_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "max_form_memory_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.FormDataParser.max_form_memory_size", "name": "max_form_memory_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "max_form_parts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.FormDataParser.max_form_parts", "name": "max_form_parts", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "stream", "mimetype", "content_length", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.FormDataParser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "stream", "mimetype", "content_length", "options"], "arg_types": ["werkzeug.formparser.FormDataParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of FormDataParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "werkzeug.formparser.t_parse_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_from_environ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "environ"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.FormDataParser.parse_from_environ", "name": "parse_from_environ", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "environ"], "arg_types": ["werkzeug.formparser.FormDataParser", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_from_environ of FormDataParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "werkzeug.formparser.t_parse_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "silent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.FormDataParser.silent", "name": "silent", "type": "builtins.bool"}}, "stream_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.FormDataParser.stream_factory", "name": "stream_factory", "type": "werkzeug.formparser.TStreamFactory"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.formparser.FormDataParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.formparser.FormDataParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.headers.Headers", "kind": "Gdef"}, "MultiDict": {".class": "SymbolTableNode", "cross_ref": "werkzeug.datastructures.structures.MultiDict", "kind": "Gdef"}, "MultiPartParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.formparser.MultiPartParser", "name": "MultiPart<PERSON><PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.MultiPartParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "werkzeug.formparser", "mro": ["werkzeug.formparser.MultiPartParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "stream_factory", "max_form_memory_size", "cls", "buffer_size", "max_form_parts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.MultiPartParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "stream_factory", "max_form_memory_size", "cls", "buffer_size", "max_form_parts"], "arg_types": ["werkzeug.formparser.MultiPartParser", {".class": "UnionType", "items": ["werkzeug.formparser.TStreamFactory", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultiPartParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buffer_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.MultiPartParser.buffer_size", "name": "buffer_size", "type": "builtins.int"}}, "cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.MultiPartParser.cls", "name": "cls", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}}}}, "fail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.MultiPartParser.fail", "name": "fail", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["werkzeug.formparser.MultiPartParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail of MultiPart<PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_part_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.MultiPartParser.get_part_charset", "name": "get_part_charset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["werkzeug.formparser.MultiPartParser", "werkzeug.datastructures.headers.Headers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_part_charset of MultiPartParser", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_form_memory_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.MultiPartParser.max_form_memory_size", "name": "max_form_memory_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "max_form_parts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.MultiPartParser.max_form_parts", "name": "max_form_parts", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "stream", "boundary", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.MultiPartParser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "stream", "boundary", "content_length"], "arg_types": ["werkzeug.formparser.MultiPartParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of MultiPartParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_file_streaming": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "total_content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.MultiPartParser.start_file_streaming", "name": "start_file_streaming", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "total_content_length"], "arg_types": ["werkzeug.formparser.MultiPartParser", "werkzeug.sansio.multipart.File", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_file_streaming of MultiPartParser", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "werkzeug.formparser.MultiPartParser.stream_factory", "name": "stream_factory", "type": "werkzeug.formparser.TStreamFactory"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.formparser.MultiPartParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.formparser.MultiPartParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartDecoder": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.multipart.MultipartDecoder", "kind": "Gdef"}, "NeedData": {".class": "SymbolTableNode", "cross_ref": "werkzeug.sansio.multipart.NeedData", "kind": "Gdef"}, "RequestEntityTooLarge": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.RequestEntityTooLarge", "kind": "Gdef"}, "SpooledTemporaryFile": {".class": "SymbolTableNode", "cross_ref": "tempfile.SpooledTemporaryFile", "kind": "Gdef"}, "TStreamFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "werkzeug.formparser.TStreamFactory", "name": "TStreamFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "werkzeug.formparser.TStreamFactory", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "werkzeug.formparser", "mro": ["werkzeug.formparser.TStreamFactory", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "total_content_length", "content_type", "filename", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "werkzeug.formparser.TStreamFactory.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "total_content_length", "content_type", "filename", "content_length"], "arg_types": ["werkzeug.formparser.TStreamFactory", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of TStreamFactory", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "werkzeug.formparser.TStreamFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "werkzeug.formparser.TStreamFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TemporaryFile": {".class": "SymbolTableNode", "cross_ref": "tempfile.TemporaryFile", "kind": "Gdef"}, "WSGIEnvironment": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIEnvironment", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.formparser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.formparser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.formparser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.formparser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.formparser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "werkzeug.formparser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_chunk_iter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["read", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser._chunk_iter", "name": "_chunk_iter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["read", "size"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_chunk_iter", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_plain_int": {".class": "SymbolTableNode", "cross_ref": "werkzeug._internal._plain_int", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "default_stream_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["total_content_length", "content_type", "filename", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.default_stream_factory", "name": "default_stream_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["total_content_length", "content_type", "filename", "content_length"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_stream_factory", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_content_length": {".class": "SymbolTableNode", "cross_ref": "werkzeug.wsgi.get_content_length", "kind": "Gdef"}, "get_input_stream": {".class": "SymbolTableNode", "cross_ref": "werkzeug.wsgi.get_input_stream", "kind": "Gdef"}, "parse_form_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 5], "arg_names": ["environ", "stream_factory", "max_form_memory_size", "max_content_length", "cls", "silent", "max_form_parts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "werkzeug.formparser.parse_form_data", "name": "parse_form_data", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 5], "arg_names": ["environ", "stream_factory", "max_form_memory_size", "max_content_length", "cls", "silent", "max_form_parts"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, {".class": "UnionType", "items": ["werkzeug.formparser.TStreamFactory", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_form_data", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "werkzeug.formparser.t_parse_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_options_header": {".class": "SymbolTableNode", "cross_ref": "werkzeug.http.parse_options_header", "kind": "Gdef"}, "parse_qsl": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qsl", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "t_parse_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "werkzeug.formparser.t_parse_result", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "werkzeug.datastructures.structures.MultiDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "te": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\werkzeug\\formparser.py"}