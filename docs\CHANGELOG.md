# Changelog

All notable changes to the LLM Proxy Server project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Enhanced Host Status Labeling**: Hosts now show proper status indicators distinguishing between configured and orphaned hosts
  - Configured hosts show as "Healthy" or "Unhealthy" based on health checks
  - Orphaned hosts (no longer in configuration) show as "Orphaned" with distinct styling
  - Added `is_configured` and `status` fields to host statistics
- **Orphaned Host Cleanup**: Automatic and manual cleanup of metrics for hosts no longer in configuration
  - Automatic cleanup on server startup
  - Manual cleanup via "🧹 Cleanup Old Hosts" button in Performance Profiling tab
  - API endpoint `/proxy/metrics/cleanup` for programmatic cleanup
- **Enhanced Model Registry Management**: Improved model discovery and cache invalidation
  - Added `refresh_model_registry()` method for manual model registry refresh
  - Automatic model registry refresh after model installation/removal operations
  - Periodic model registry refresh in health check loop (every 5 cycles)
  - Added `refresh` parameter to `/api/tags` endpoint for on-demand model list refresh
- **Performance Profiling Enhancements**: Improved reliability and user experience
  - Enhanced host-model compatibility filtering with real-time validation
  - Added model registry refresh functionality in profiling interface
  - Improved error handling and status reporting
  - Better visual feedback for orphaned vs configured hosts
- **Development Environment Improvements**: Enhanced Docker Compose configuration for live development
  - Added source code volume mounting for live editing (`./llm_proxy_server:/app/llm_proxy_server`)
  - Improved debugging capabilities with better logging and debug endpoints
- **Comprehensive Marketing & Sales Literature**: Complete business documentation suite
  - **Product Overview**: Executive summary with value propositions and target markets
  - **Business Case**: Detailed ROI analysis and financial justification ($450K-$1.7M annual savings)
  - **Competitive Analysis**: Market positioning against LiteLLM, OpenRouter, AWS Bedrock
  - **Sales Playbook**: Complete sales process with objection handling and demo strategies
  - **Case Study Template**: Structured framework for customer success documentation
- **Reasoning Model Support**: Native integration with reasoning models (DeepSeek-R1, Qwen3, O1)
  - Automatic detection of reasoning models by name patterns
  - Native `think` parameter support for Ollama v0.9.0+
  - Separate thinking process display with real-time streaming
  - Collapsible thinking sections for clean reading experience
  - Added `thinking`, `reasoning_tokens`, `reasoning_duration` fields to response models
- **Enhanced Mathematical Formatting**: Comprehensive LaTeX and mathematical notation rendering
  - LaTeX inline math expressions `\( ... \)` with blue highlighting
  - LaTeX display math expressions `\[ ... \]` with centered formatting
  - Boxed answers `\boxed{...}` with styled answer boxes
  - Proper fraction rendering `\frac{a}{b}` with horizontal bars
  - Automatic superscript conversion (`x2` → `x²`, `(5)2` → `(5)²`)
  - Mathematical symbol conversion (×, ÷, ±, √, etc.)
  - Square root styling with overline formatting
  - Added quadratic equation test prompt to Mathematics section

### Changed
- **Metrics System Architecture**: Enhanced metrics collection to support host status determination
  - Modified `get_host_stats()` to accept `configured_hosts` parameter
  - Updated `/proxy/metrics` endpoint to pass current host configuration
  - Improved metrics generation with better error handling
- **Host Health Monitoring**: Enhanced health check system with better status reporting
  - Health checks now distinguish between configured and orphaned hosts
  - Improved visual indicators in web interface
  - Better error reporting and status messages
- **Pydantic Serialization**: Updated from deprecated `dict()` to `model_dump()` method
  - Fixed critical serialization issue that was losing thinking field data
  - Improved JSON serialization reliability for streaming responses
  - Enhanced compatibility with newer Pydantic versions
- **Ollama Integration**: Enhanced integration with Ollama's native thinking mode
  - Added `GenerateRequest` import for proper type handling
  - Improved type annotations for better code maintainability
  - Enhanced stream processing for thinking content extraction

### Fixed
- **Host Status Display**: Resolved issue where all hosts showed incorrect status labels
  - Fixed orphaned host detection logic
  - Corrected status field population in metrics response
  - Improved CSS styling for different host states
- **Model Registry Synchronization**: Fixed issues with model registry not reflecting changes
  - Resolved cache invalidation problems after model operations
  - Fixed model list not updating after installation/removal
  - Improved synchronization between different system components
- **Type Safety Issues**: Fixed type error in model discovery
  - Added null filtering for model list comprehension in `_discover_models()` method
  - Resolved "Invalid index type Optional[str]" error on line 105 of proxy_manager.py
- **Thinking Content Display**: Fixed issues with reasoning model thinking process display
  - Resolved Pydantic serialization bug that was losing thinking field data
  - Fixed frontend thinking content accumulation from streaming chunks
  - Improved real-time thinking display with proper formatting
- **Mathematical Formatting**: Enhanced LaTeX and mathematical notation rendering
  - Fixed superscript conversion patterns for better coverage
  - Improved fraction display with proper horizontal bars
  - Enhanced square root and mathematical symbol rendering - December 2024

### 🔄 Major: Hybrid Configuration System

**Breaking Change**: Configuration system has been redesigned for better maintainability and environment management.

#### What Changed
- **NEW**: `.env` file for base configuration settings
- **NEW**: Environment-specific overrides in Docker Compose files
- **IMPROVED**: Clear separation between base settings and environment-specific overrides
- **IMPROVED**: Centralized configuration management

#### Migration Required
Existing deployments need to:
1. Create `.env` file with base settings (see `.env.example`)
2. Update Docker Compose files to use `env_file` directive
3. Move environment-specific settings to Docker Compose `environment` section

#### Files Added/Modified
- **NEW**: `.env` - Base configuration for all environments
- **UPDATED**: `.env.example` - Comprehensive configuration template
- **UPDATED**: `docker-compose.local.yml` - Uses hybrid approach
- **UPDATED**: `docker-compose.yml` - Uses hybrid approach  
- **NEW**: `docker-compose.staging.yml` - Staging environment configuration

### 🆕 Feature: Enhanced Chat Interface with Dynamic Model Lists

#### What Changed
- **NEW**: Model dropdown dynamically loads based on host selection
- **NEW**: "Auto (Load Balanced)" shows all models from all hosts
- **NEW**: Host-specific selection shows only models from that host
- **NEW**: Models are sorted alphabetically for better UX
- **NEW**: Enhanced performance metrics with host names and descriptions
- **IMPROVED**: Real-time model list updates when switching hosts
- **IMPROVED**: Better user experience with server information display

#### Technical Implementation
- Uses `/api/tags` for all models (load balanced mode)
- Uses `/config/hosts/models` for host-specific models
- JavaScript `loadChatModels()` function handles dynamic loading
- Event listeners update model list on host selection changes
- Enhanced performance display with host metadata

#### User Experience Improvements
- **Model Discovery**: Users can see all available models across infrastructure
- **Host-Specific Testing**: Easy testing of specific hardware configurations
- **Performance Insights**: Detailed metrics including host information
- **Intuitive Interface**: Clear separation between load balanced and direct routing modes

#### Files Modified
- **UPDATED**: `static/config.html` - Enhanced model loading logic and UI improvements

### 📚 Documentation Consolidation

#### What Changed
- **UPDATED**: `docs/configuration.md` - Comprehensive configuration reference
- **UPDATED**: `config/README.md` - Simplified, points to main docs
- **NEW**: Migration guide for hybrid configuration
- **NEW**: Environment-specific deployment examples
- **IMPROVED**: Clear documentation hierarchy

#### Documentation Structure
```
docs/
├── configuration.md     # MAIN REFERENCE - All configuration options
├── CHANGELOG.md        # This file - Recent changes
└── [other guides]      # Specialized topics

config/
└── README.md          # Quick reference, points to main docs
```

### 🔧 Configuration Precedence

The new system uses this precedence (highest to lowest):
1. **Environment variables in docker-compose files** (overrides)
2. **Values in `.env` file** (base settings)  
3. **Code defaults in `config.py`** (fallbacks)

### 🚀 Deployment Examples

#### Development
```bash
docker-compose -f docker-compose.local.yml up
# Uses: DEBUG=true, LOG_LEVEL=DEBUG, base settings from .env
```

#### Production  
```bash
docker-compose up
# Uses: DEBUG=false, optimized settings, base settings from .env
```

#### Staging
```bash
docker-compose -f docker-compose.staging.yml up  
# Uses: DEBUG=true, AUTH_ENABLED=true, reduced capacity
```

### 🐛 Bug Fixes

#### Host Form Submission (Fixed Earlier)
- **FIXED**: Host editing form wasn't saving `name` and `description` fields
- **ROOT CAUSE**: JavaScript form handler missing field collection
- **SOLUTION**: Added missing fields to `hostData` object

#### Limited Model List (Fixed Earlier)
- **FIXED**: Chat interface showed only previously used models
- **ROOT CAUSE**: Using `metrics.model_stats` instead of full model list
- **SOLUTION**: Changed to use `/api/tags` endpoint for complete model discovery

### 💡 Benefits of Changes

#### For Developers
- ✅ **Simplified configuration management** - Base settings in one place
- ✅ **Environment-specific customization** - Easy overrides per environment
- ✅ **Better development experience** - Clear separation of concerns
- ✅ **Improved model discovery** - See all available models dynamically

#### For Operations
- ✅ **Easier deployment** - Environment switching via compose file selection
- ✅ **Centralized base configuration** - Common settings managed once
- ✅ **Clear configuration hierarchy** - Understand what overrides what
- ✅ **Better documentation** - Consolidated reference materials

#### For Users
- ✅ **Better model selection** - See all models or host-specific models
- ✅ **Alphabetical sorting** - Easier to find models
- ✅ **Dynamic updates** - Model list updates when switching hosts
- ✅ **Improved UX** - More intuitive interface behavior

### 🔮 Next Steps

Recommended follow-up actions:
1. **Test hybrid configuration** across all environments
2. **Validate model loading** in different host configurations  
3. **Update deployment scripts** to use new configuration approach
4. **Consider additional environments** (testing, staging variants)
5. **Monitor performance** of dynamic model loading

### 📋 Compatibility

#### Backward Compatibility
- **JSON configuration files**: No changes required
- **API endpoints**: All existing endpoints work unchanged
- **Authentication**: No changes to user management
- **Load balancing**: All strategies work as before

#### Breaking Changes
- **Docker Compose files**: Need to adopt `env_file` approach
- **Environment variable management**: Should migrate to hybrid approach
- **Configuration documentation**: Old references may be outdated

---

## [Unreleased] - 2025-07-27

### Added
- **Enhanced System Settings Display**: Comprehensive configuration overview with organized sections
  - **Server Configuration**: Host, port, debug mode, CORS settings display
  - **Web Management Interface**: URLs, port mapping, static file configuration
  - **Load Balancing**: Strategy, limits, timeouts, host configuration paths
  - **Authentication & Security**: Status and configuration file paths
  - **Health Monitoring**: Check intervals and timeout settings
  - **Logging & Metrics**: Log levels, file paths, metrics collection status
  - **Docker Configuration**: Port mappings and container information
  - Visual organization with section headers, emoji icons, and configuration notes
  - Enhanced `/config/settings` API endpoint with additional configuration parameters
  - Professional styling with organized sections and helpful information
- **Host Selection API**: New `host` parameter for direct server targeting
  - Added optional `host` field to `GenerateRequest` model for bypassing load balancing
  - Implemented host validation and availability checking in proxy manager
  - Added support for targeting specific high-performance servers
- **Interactive Chat Interface**: Real-time model testing and performance profiling
  - Modal-based chat interface accessible from Hosts tab
  - Model and host selection dropdowns for targeted testing
  - Real-time performance metrics display (response time, tokens/sec, host used)
  - Direct performance comparison across different hosts
- **Host-Specific Model Performance**: Enhanced model cards with per-host metrics
  - Model cards now display performance data specific to each host
  - Performance calculation from recent activity data for accuracy
  - Fallback to aggregated statistics when recent data is limited
- **Collapsible Tree Structure for Model Statistics**: Interactive hierarchical display
  - Model Usage Statistics table now uses expandable tree structure
  - Model rows (parents) show aggregated statistics across all hosts
  - Host rows (children) show individual host performance for each model
  - Click-to-expand functionality with smooth animations and visual feedback
  - Reduced table clutter by eliminating duplicate model names
  - Enhanced user experience with clear parent-child relationships
- **Comprehensive Documentation Updates**: Enhanced all documentation with new features
  - Updated `api-reference.md` with host selection parameter and examples
  - Enhanced `monitoring.md` with host-specific model performance tracking
  - Updated `web-configuration.md` with chat interface documentation
  - Enhanced `configuration.md` with host selection configuration and validation rules
  - Enhanced `development.md` with local development setup documentation
- **Project Licensing**: Added MIT License file to establish clear open-source licensing
  - Added `LICENSE` file with standard MIT License terms
  - Consistent with existing license declarations in `setup.py` and `pyproject.toml`
  - Proper copyright attribution and permissions
- **Legal Protection**: Added comprehensive disclaimer of responsibility by authors
  - Extended `LICENSE` file with detailed disclaimer covering data loss, security, AI outputs, and production use
  - Added disclaimer sections to `README.md` and `docs/index.md` for visibility
  - Clear guidance on user responsibilities for testing, security, and compliance

### Changed
- **Enhanced Model Card Layout**: Improved CSS for better content display
  - Added word-wrap and overflow-wrap for better text handling
  - Implemented compact performance section design
  - Fixed layout issues with wide content in model cards
- **JavaScript Error Handling**: Improved error handling and variable scoping
  - Fixed variable naming conflict in `apiCall` function (`data` → `responseData`)
  - Added try-catch blocks around metrics calculations
  - Enhanced debugging with request/response logging
- **Development Workflow**: Switched to local development configuration
  - Using `docker-compose.local.yml` for development with volume mounting
  - Enhanced debug logging and live code editing capabilities
  - Improved development experience with static file mounting

### Technical Improvements
- **Backend API Enhancement**: Extended proxy manager with host selection capabilities
  - Modified `_get_client_for_model()` to accept `preferred_host` parameter
  - Added host validation logic with proper error handling
  - Updated generate endpoints to pass host parameter through request chain
- **Frontend Performance Optimization**: Enhanced metrics calculation and display
  - Implemented `getModelMetricsForHost()` for accurate per-host model performance
  - Added `fetchMetricsData()` helper for concurrent API calls
  - Enhanced model card generation with performance sections
- **Error Handling and Debugging**: Improved troubleshooting capabilities
  - Added debug endpoints for request parsing verification
  - Enhanced logging for host selection and validation
  - Improved error messages for host availability issues
- **Test Coverage Enhancement**: Comprehensive load balancer testing suite
  - Added tests for "fastest" and "adaptive" selection strategies
  - Implemented response time recording and statistics functionality tests
  - Added edge case testing for weighted random selection (zero weights, fallback scenarios)
  - Added unknown strategy fallback behavior verification
  - Added multi-model round-robin index isolation testing
  - Added host health change logging verification
  - Improved load balancer test coverage from 61% to 97%

### Fixed
- **Authentication Bug**: Fixed `is_user_admin()` method comparing boolean values to string literals
  - Changed comparison from `== 'true'` to `is True` for proper boolean evaluation
  - Resolves failing test `test_is_user_admin` in authentication test suite
  - Ensures admin privilege checking works correctly for user authorization
- **Test Isolation Issues**: Fixed monitoring tests failing due to persistent metrics data
  - Modified `test_init` to use temporary metrics file for clean initialization
  - Updated `metrics_manager` fixture to use temporary files for test isolation
  - Removed redundant `time` import in thread safety test
  - Ensures all monitoring tests start with clean state
- **API Integration Test Fixes**: Fixed health endpoint test expecting "healthy" status
  - Added mock for `get_health_status()` to return predictable test data
  - Fixed unused `headers` variables in authentication tests
  - Resolves test failures due to missing hosts configuration in test environment

### Use Cases Enabled
- **Performance Testing**: Direct targeting of high-performance servers for demanding workloads
- **Capacity Planning**: Real-time performance comparison across different hardware configurations
- **Hardware Optimization**: Identification of best-performing host-model combinations
- **Interactive Profiling**: Live chat interface for immediate performance feedback
- **Resource Management**: Intelligent routing based on server capabilities and model requirements
- **Web UI Enhancements**: Documented compact display formats, enhanced tables, and performance cards
- **Load Balancing Insights**: Added strategy impacts on performance assessment and profiling recommendations
- **API Response Examples**: Updated with realistic token performance data and host-specific statistics

---

## [1.0.0] - 2025-07-24

### Added
- **Health Check Filtering**: Separated health check requests from main request statistics
  - Health checks (`GET /proxy/health`) are now tracked separately
  - Prevents Docker health checks from overwhelming usage analytics
  - Added `health_check_count` and `health_check_errors` to metrics API
- **Model-Host Correlation**: Track which hosts serve which models
  - Added `host_usage` and `primary_host` fields to model statistics
  - Enhanced web interface to display host information in model usage table
- **Enhanced Web Management Interface**: Improved dashboard with categorized request breakdown
  - Added request categorization (AI Requests, Health Monitoring, System Monitoring, API Discovery)
  - Clearer percentage calculations excluding health checks from main stats
  - Better visual indicators and error display
- **Metrics Persistence**: Automatic disk storage with manual controls
  - Auto-save every 10 requests to `data/metrics.json`
  - Manual save/reset controls via web interface and API endpoints
  - Metrics survive server restarts
- **Web Interface Alias**: Added `/config` route as alias for `/admin`
- **Initial Release Features**:
  - Basic load balancing across multiple Ollama servers
  - Web-based configuration interface
  - Authentication system
  - Multiple load balancing strategies
  - Health monitoring and metrics

### Changed
- **Request Statistics**: Main request stats now exclude health checks for clearer usage patterns
- **Metrics API Response**: Enhanced with separated health check counters and model-host correlation
- **Web Interface Display**: Improved categorization and percentage calculations
- **Documentation**: Updated monitoring guide with new features and examples

### Fixed
- **Data Consistency**: Resolved duplicate request recording between proxy_manager and main modules
- **Endpoint Naming**: Standardized endpoint names for consistent metrics recording
- **Health Check Timestamps**: Improved timestamp handling for health check records
- **Browser Caching**: Added proper static file volume mounting for Docker deployments

### Technical Details
- Modified `monitoring.py` to separate health check tracking from main request metrics
- Enhanced `config.html` with improved request breakdown and categorization
- Updated Docker Compose configurations with proper volume mounts
- Standardized request recording to single source of truth in proxy_manager

### Migration Notes
- Existing metrics will be automatically migrated on first startup
- Health checks will be separated from existing request counts
- No breaking changes to API endpoints or configuration

**SVN Revision**: r1 (2025-07-24 09:42:00 -0400)
**Commit Message**: "initial check-in after code generation"
