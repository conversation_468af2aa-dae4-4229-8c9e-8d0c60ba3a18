{"data_mtime": 1753889303, "dep_lines": [12, 21, 22, 23, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["wsproto.events", "wsproto.extensions", "wsproto.frame_protocol", "wsproto.utilities", "collections", "enum", "typing", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "34c1c5e4f7636bd6734f13e2648bc601163a8c98", "id": "wsproto.connection", "ignore_all": true, "interface_hash": "64eb8171c0369d76e72fb861b98f8e9b79ba3b04", "mtime": 1708667623, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\wsproto\\connection.py", "plugin_data": null, "size": 6813, "suppressed": [], "version_id": "1.15.0"}